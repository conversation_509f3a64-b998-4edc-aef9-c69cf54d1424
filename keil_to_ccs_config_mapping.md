# Keil到CCS项目配置映射表

## 项目基本信息
- **项目名称**: MPU6050_I2C
- **目标设备**: MSPM0G3507 (Cortex-M0+)
- **编译器**: ARM Compiler 6.23 (ARMCLANG) → TI ARM Compiler
- **设备包**: TexasInstruments.MSPM0G1X0X_G3X0X_DFP.1.3.1

## 内存配置映射

### Keil Scatter File (.sct) → CCS Linker Command File (.cmd)

| 区域名称 | 起始地址 | 大小 | 用途 | CCS等效配置 |
|---------|---------|------|------|------------|
| LR_IROM1/ER_IROM1 | 0x00000000 | 0x20000 (128KB) | 主Flash区域 | FLASH: origin=0x00000000, length=0x20000 |
| RW_IRAM2 | 0x20200000 | 0x8000 (32KB) | RAM数据区 | SRAM: origin=0x20200000, length=0x8000 |
| LR_BCR/BCR_CONFIG | 0x41C00000 | 0x80 | BCR配置区 | BCR_CONFIG: origin=0x41C00000, length=0x80 |
| LR_BSL/BSL_CONFIG | 0x41C00100 | 0x80 | BSL配置区 | BSL_CONFIG: origin=0x41C00100, length=0x80 |

### 完整内存映射
- **IRAM**: 0x20000000, 0x8000 (32KB) - 第一个RAM区域
- **IRAM2**: 0x20200000, 0x8000 (32KB) - 第二个RAM区域 (主要使用)
- **IROM**: 0x00000000, 0x20000 (128KB) - 主Flash区域
- **IROM2**: 0x00400000, 0x20000 (128KB) - 第二个Flash区域

## 编译器设置映射

### C编译器标志
| Keil ARM Compiler 6 | TI ARM Compiler | 说明 |
|---------------------|-----------------|------|
| -xc -std=c99 | -xc -std=c99 | C语言标准 |
| --target=arm-arm-none-eabi | --abi=eabi | 目标ABI |
| -mcpu=cortex-m0plus | --cpu=cortex-m0plus | 目标CPU |
| -O2 | -O2 | 优化级别 |
| -ffunction-sections | --function_sections | 函数分段 |
| -gdwarf-4 | -g | 调试信息 |
| -fno-rtti | (不需要) | C++特性，C项目不需要 |
| -funsigned-char | --unsigned_chars | 无符号char |
| -fshort-enums | --enum_type=int | 枚举类型 |
| -fshort-wchar | (默认) | 宽字符类型 |

### 汇编器标志
| Keil | TI | 说明 |
|------|----|----|
| --target=arm-arm-none-eabi | --abi=eabi | 目标ABI |
| -mcpu=cortex-m0plus | --cpu=cortex-m0plus | 目标CPU |
| -masm=auto | (自动检测) | 汇编语法 |

### 预处理器定义
| Keil定义 | CCS等效定义 | 说明 |
|---------|------------|------|
| __UVISION_VERSION="542" | (移除) | Keil特定定义 |
| __MSPM0G3507__ | __MSPM0G3507__ | 设备定义(保持) |

## 包含路径映射

### Keil相对路径 → CCS相对路径
| Keil路径 | CCS路径 | 说明 |
|---------|---------|------|
| ..\..\source\third_party\CMSIS\Core\Include | ../../source/third_party/CMSIS/Core/Include | CMSIS头文件 |
| ..\..\source | ../../source | TI源码目录 |
| ..\BSP | ../BSP | BSP驱动目录 |
| ..\..\10_I2C | ../../10_I2C | I2C示例目录 |
| ..\BSP\eMPL | ../BSP/eMPL | MPU6050库目录 |

## 库文件映射

### 库文件路径转换
| Keil库文件 | CCS库文件 | 说明 |
|-----------|-----------|------|
| ../../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a | ../../source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.lib | TI DriverLib库 |

## 源文件组织结构

### 文件组映射
| Keil组名 | 文件列表 | CCS等效组织 |
|---------|---------|------------|
| Source | empty.c, empty.syscfg, startup_mspm0g350x_uvision.s, ti_msp_dl_config.h/c, board.c, ALLHeader.h | 源文件夹 |
| BSP | bsp_mpu6050.c, inv_mpu.c, inv_mpu_dmp_motion_driver.c, drv_oled.c, ssd1306.c, glcdfont.c, ccd.c, huiduo.c, ADC.c, Control.c, Motor.c | BSP文件夹 |
| Driverlib | dl_*.c文件 | ti文件夹 |

## 关键配置参数

### 时钟配置
- **CPU时钟**: 32MHz (CPUCLK_FREQ = 32000000)
- **系统时钟**: SYSOSC_FREQ_BASE
- **定时器时钟**: BUSCLK / 8, 预分频200

### 外设配置
- **UART0**: 9600波特率, PA10(TX), PA11(RX)
- **ADC**: 双通道配置 (AO, ADC1)
- **GPIO**: 多个GPIO配置用于LED、电机控制等
- **TIMER**: 5ms周期定时器，用于控制循环
- **I2C**: PA0(SCL), PA1(SDA) - MPU6050通信

### 中断配置
- **TIMER_0**: 优先级0, 5ms周期中断
- **ADC1**: ADC转换完成中断
- **UART0**: 接收中断

## 转换注意事项

1. **路径分隔符**: Keil使用反斜杠(\)，CCS使用正斜杠(/)
2. **编译器差异**: 某些警告抑制选项在TI编译器中不同
3. **启动文件**: 需要从ARM汇编语法转换为TI汇编语法或使用TI标准启动文件
4. **SysConfig集成**: 确保.syscfg文件在CCS中正确集成和自动生成
5. **调试配置**: 使用XDS110调试器替代Keil调试器

## 项目依赖关系图

### 核心依赖链
```
empty.c (主程序)
├── board.h → ti_msp_dl_config.h (TI DriverLib配置)
├── ALLHeader.h (项目头文件集合)
│   ├── ti_msp_dl_config.h
│   ├── usart.h
│   ├── bsp_mpu6050.h (MPU6050传感器)
│   ├── inv_mpu.h (MPU6050 DMP库)
│   ├── drv_oled.h (OLED显示)
│   ├── huiduo.h (灰度传感器)
│   ├── Motor.h (电机控制)
│   ├── Control.h (控制算法)
│   └── ADC.h (ADC采集)
├── bsp_mpu6050.h
├── inv_mpu.h
└── drv_oled.h
```

### BSP模块依赖
```
BSP/
├── bsp_mpu6050.c → inv_mpu.h, ALLHeader.h
├── inv_mpu.c → inv_mpu.h (eMPL库)
├── inv_mpu_dmp_motion_driver.c → inv_mpu_dmp_motion_driver.h
├── drv_oled.c → drv_oled.h, ssd1306.h
├── ssd1306.c → ssd1306.h, binary.h, glcdfont.c
├── huiduo.c → huiduo.h, ALLHeader.h
├── Motor.c → Motor.h, ALLHeader.h
├── Control.c → Control.h, ALLHeader.h
└── ADC.c → ADC.h, ALLHeader.h
```

### TI DriverLib依赖
```
ti_msp_dl_config.h/c (SysConfig生成)
├── ti/driverlib/driverlib.h
├── ti/devices/msp/msp.h
└── ti/driverlib/m0p/dl_core.h

TI DriverLib模块:
├── dl_adc12.c/h (ADC功能)
├── dl_gpio.h (GPIO控制)
├── dl_timer.c/h (定时器)
├── dl_uart.c/h (串口通信)
├── dl_i2c.c/h (I2C通信)
└── dl_common.c/h (通用功能)
```

### 关键文件分析

#### 1. 配置文件
- **empty.syscfg**: SysConfig配置文件，定义外设配置
- **ti_msp_dl_config.h/c**: 自动生成的配置代码
- **mspm0g3507.sct**: Keil链接脚本

#### 2. 启动和系统文件
- **startup_mspm0g350x_uvision.s**: ARM汇编启动文件
- **board.c/h**: 板级初始化和基础功能

#### 3. 应用模块
- **empty.c**: 主程序，包含main函数和中断处理
- **BSP/**: 硬件抽象层，包含各种传感器和外设驱动

## 编译器兼容性分析

### 需要适配的编译器特性
| 特性 | Keil ARM Compiler 6 | TI ARM Compiler | 适配方案 |
|------|-------------------|-----------------|---------|
| 内联汇编 | __asm | __asm | 语法兼容 |
| 属性声明 | __attribute__ | __attribute__ | 语法兼容 |
| 编译指示 | #pragma | #pragma | 大部分兼容 |
| 警告控制 | -Wno-xxx | --diag_suppress=xxx | 需要映射 |
| 优化选项 | -O2 | -O2 | 兼容 |

### 潜在兼容性问题
1. **汇编语法**: ARM汇编 vs TI汇编语法差异
2. **库函数**: 某些ARM特定库函数可能不兼容
3. **链接器脚本**: scatter file vs linker command file语法差异
4. **调试信息**: DWARF格式可能有差异

## 验证检查点

- [ ] 所有包含路径正确解析
- [ ] 预处理器定义正确设置
- [ ] 内存配置与原始配置一致
- [ ] 库文件正确链接
- [ ] SysConfig工具正常工作
- [ ] 编译无错误和警告
- [ ] 生成的二进制文件大小合理
- [ ] 依赖关系图完整无循环依赖
- [ ] 所有BSP模块正确编译
- [ ] TI DriverLib集成正常
