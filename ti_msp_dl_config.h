/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3505

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     32000000



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMG0)
#define TIMER_0_INST_IRQHandler                                 TIMG0_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMG0_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                            (99U)



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                            4000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_4_MHZ_9600_BAUD                                         (26)
#define UART_0_FBRD_4_MHZ_9600_BAUD                                          (3)





/* Defines for AO */
#define AO_INST                                                             ADC0
#define AO_INST_IRQHandler                                       ADC0_IRQHandler
#define AO_INST_INT_IRQN                                         (ADC0_INT_IRQn)
#define AO_ADCMEM_CH0                                         DL_ADC12_MEM_IDX_0
#define AO_ADCMEM_CH0_REF                        DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define AO_ADCMEM_CH0_REF_VOLTAGE_V                                          3.3
#define GPIO_AO_C0_PORT                                                    GPIOA
#define GPIO_AO_C0_PIN                                            DL_GPIO_PIN_27

/* Defines for ADC1 */
#define ADC1_INST                                                           ADC1
#define ADC1_INST_IRQHandler                                     ADC1_IRQHandler
#define ADC1_INST_INT_IRQN                                       (ADC1_INT_IRQn)
#define ADC1_ADCMEM_ADC_Channel0                              DL_ADC12_MEM_IDX_0
#define ADC1_ADCMEM_ADC_Channel0_REF             DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC1_ADCMEM_ADC_Channel0_REF_VOLTAGE_V                                     3.3
#define GPIO_ADC1_C0_PORT                                                  GPIOA
#define GPIO_ADC1_C0_PIN                                          DL_GPIO_PIN_15



/* Port definition for Pin Group LED1 */
#define LED1_PORT                                                        (GPIOB)

/* Defines for PIN_2: GPIOB.2 with pinCMx 15 on package pin 50 */
#define LED1_PIN_2_PIN                                           (DL_GPIO_PIN_2)
#define LED1_PIN_2_IOMUX                                         (IOMUX_PINCM15)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOA)

/* Defines for PIN_18: GPIOA.18 with pinCMx 40 on package pin 11 */
// pins affected by this interrupt request:["PIN_18"]
#define KEY_INT_IRQN                                            (GPIOA_INT_IRQn)
#define KEY_INT_IIDX                            (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define KEY_PIN_18_IIDX                                     (DL_GPIO_IIDX_DIO18)
#define KEY_PIN_18_PIN                                          (DL_GPIO_PIN_18)
#define KEY_PIN_18_IOMUX                                         (IOMUX_PINCM40)
/* Port definition for Pin Group PORTASDA */
#define PORTASDA_PORT                                                    (GPIOA)

/* Defines for OLED_SDA: GPIOA.28 with pinCMx 3 on package pin 35 */
#define PORTASDA_OLED_SDA_PIN                                   (DL_GPIO_PIN_28)
#define PORTASDA_OLED_SDA_IOMUX                                   (IOMUX_PINCM3)
/* Port definition for Pin Group PORTASCL */
#define PORTASCL_PORT                                                    (GPIOA)

/* Defines for OLED_SCL: GPIOA.31 with pinCMx 6 on package pin 39 */
#define PORTASCL_OLED_SCL_PIN                                   (DL_GPIO_PIN_31)
#define PORTASCL_OLED_SCL_IOMUX                                   (IOMUX_PINCM6)
/* Port definition for Pin Group USER_GPIO */
#define USER_GPIO_PORT                                                   (GPIOA)

/* Defines for LED_PA0: GPIOA.13 with pinCMx 35 on package pin 6 */
#define USER_GPIO_LED_PA0_PIN                                   (DL_GPIO_PIN_13)
#define USER_GPIO_LED_PA0_IOMUX                                  (IOMUX_PINCM35)
/* Port definition for Pin Group I2C */
#define I2C_PORT                                                         (GPIOA)

/* Defines for SCL: GPIOA.0 with pinCMx 1 on package pin 33 */
#define I2C_SCL_PIN                                              (DL_GPIO_PIN_0)
#define I2C_SCL_IOMUX                                             (IOMUX_PINCM1)
/* Defines for SDA: GPIOA.1 with pinCMx 2 on package pin 34 */
#define I2C_SDA_PIN                                              (DL_GPIO_PIN_1)
#define I2C_SDA_IOMUX                                             (IOMUX_PINCM2)
/* Port definition for Pin Group CCD */
#define CCD_PORT                                                         (GPIOA)

/* Defines for CLK: GPIOA.26 with pinCMx 59 on package pin 30 */
#define CCD_CLK_PIN                                             (DL_GPIO_PIN_26)
#define CCD_CLK_IOMUX                                            (IOMUX_PINCM59)
/* Defines for SI: GPIOA.25 with pinCMx 55 on package pin 26 */
#define CCD_SI_PIN                                              (DL_GPIO_PIN_25)
#define CCD_SI_IOMUX                                             (IOMUX_PINCM55)
/* Defines for PIN_0: GPIOB.7 with pinCMx 24 on package pin 59 */
#define Gray_Address_PIN_0_PORT                                          (GPIOB)
#define Gray_Address_PIN_0_PIN                                   (DL_GPIO_PIN_7)
#define Gray_Address_PIN_0_IOMUX                                 (IOMUX_PINCM24)
/* Defines for PIN_1: GPIOB.9 with pinCMx 26 on package pin 61 */
#define Gray_Address_PIN_1_PORT                                          (GPIOB)
#define Gray_Address_PIN_1_PIN                                   (DL_GPIO_PIN_9)
#define Gray_Address_PIN_1_IOMUX                                 (IOMUX_PINCM26)
/* Defines for PIN_3: GPIOA.12 with pinCMx 34 on package pin 5 */
#define Gray_Address_PIN_3_PORT                                          (GPIOA)
#define Gray_Address_PIN_3_PIN                                  (DL_GPIO_PIN_12)
#define Gray_Address_PIN_3_IOMUX                                 (IOMUX_PINCM34)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_AO_init(void);
void SYSCFG_DL_ADC1_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
