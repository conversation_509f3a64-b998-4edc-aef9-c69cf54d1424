#ifndef  __Control_H
#define  __Control_H

#include "Control.h"


extern unsigned char OpenArt_Flag;
extern uint16_t OpenArt_Count;
extern unsigned char FoundCount ;
extern unsigned char PinState;
extern uint8_t Stop_Count;
extern uint16_t Distance_01;
extern unsigned char Pin2 ;
extern unsigned char Pin3 ;
extern int16_t Position2,AD_L_R_Diff,Pos_error_now,Pos_error_last;
extern uint8_t Turn_Left_Flag,Turn_Right_Flag,Turn180_Flag;
extern float Kp,Kd,Kd_tly,Position,Target_S,Kp_S,Kd_S;;
extern uint8_t Stop_Flag,State;
extern uint16_t Distance[4],Find_Distance;
extern uint16_t Time_Count;
extern uint8_t Pass_Flag;
extern uint8_t Zero_Flag,Stop_All_Flag;
extern uint8_t sensor[5];
void Speed_ctrl(void);
void Position_Proc(void);
void Poistion_PID(void);
int Poistion_Error(void);
int ADC_Averge(void);
void Turn_ctol(void);
void OPenArtGet(void);


#endif
