#ifndef __ALLHEADER_H
#define __ALLHEADER_H


#include <stdio.h>
#include "stdint.h"
#include <string.h>
#include "ti_msp_dl_config.h"

typedef uint8_t  u8;
typedef uint16_t u16;
typedef uint32_t u32;
// extern uint8_t CCD_Z<PERSON>zhi,CCD_Yuzhi;  // CCD相关变量已移除
extern uint16_t HDadc_value[8];
extern uint16_t HD2adc_value[8];


#include "usart.h"
// #include "ccd.h"  // CCD传感器已移除

#include "board.h"
#include <stdio.h>
#include "bsp_mpu6050.h"
#include "inv_mpu.h"
#include "drv_oled.h"
#include "huiduo.h"
#include "Motor.h"
#include "Control.h"
#include "ADC.h"

#endif


