/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const ADC122  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.repeatMode                        = true;
ADC121.sampleTime0                       = "15 ms";
ADC121.enabledInterrupts                 = ["DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED"];
ADC121.adcMem0_name                      = "CH0";
ADC121.$name                             = "AO";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric2";

ADC122.$name                             = "ADC1";
ADC122.repeatMode                        = true;
ADC122.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC122.adcMem0_name                      = "ADC_Channel0";
ADC122.peripheral.adcPin0.$assign        = "PA15";
ADC122.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);
ADC122.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric3";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                              = "LED1";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].$name            = "PIN_2";
GPIO1.associatedPins[0].assignedPin      = "2";

GPIO2.$name                              = "KEY";
GPIO2.port                               = "PORTA";
GPIO2.associatedPins[0].$name            = "PIN_18";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].assignedPin      = "18";
GPIO2.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].polarity         = "RISE";
GPIO2.associatedPins[0].pin.$assign      = "PA18";

GPIO3.$name                          = "I2C";
GPIO3.port                           = "PORTA";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name        = "SCL";
GPIO3.associatedPins[0].initialValue = "SET";
GPIO3.associatedPins[0].ioStructure  = "OD";
GPIO3.associatedPins[0].assignedPin  = "0";
GPIO3.associatedPins[0].pin.$assign  = "PA0";
GPIO3.associatedPins[1].$name        = "SDA";
GPIO3.associatedPins[1].initialValue = "SET";
GPIO3.associatedPins[1].ioStructure  = "OD";
GPIO3.associatedPins[1].assignedPin  = "1";
GPIO3.associatedPins[1].pin.$assign  = "PA1";

GPIO4.$name                         = "PORTASDA";
GPIO4.port                          = "PORTA";
GPIO4.associatedPins[0].$name       = "OLED_SDA";
GPIO4.associatedPins[0].pin.$assign = "PA28";

GPIO5.$name                         = "PORTASCL";
GPIO5.port                          = "PORTA";
GPIO5.associatedPins[0].$name       = "OLED_SCL";
GPIO5.associatedPins[0].pin.$assign = "PA31";

GPIO6.$name                   = "USER_GPIO";
GPIO6.port                    = "PORTA";
GPIO6.associatedPins[0].$name = "LED_PA0";

GPIO7.$name                          = "CCD";
GPIO7.associatedPins.create(2);
GPIO7.associatedPins[0].$name        = "CLK";
GPIO7.associatedPins[0].assignedPort = "PORTA";
GPIO7.associatedPins[0].assignedPin  = "26";
GPIO7.associatedPins[1].$name        = "SI";
GPIO7.associatedPins[1].assignedPort = "PORTA";
GPIO7.associatedPins[1].assignedPin  = "25";

GPIO8.$name                         = "Gray_Address";
GPIO8.associatedPins.create(3);
GPIO8.associatedPins[0].$name       = "PIN_0";
GPIO8.associatedPins[0].pin.$assign = "PB7";
GPIO8.associatedPins[1].$name       = "PIN_1";
GPIO8.associatedPins[1].pin.$assign = "PB9";
GPIO8.associatedPins[2].$name       = "PIN_3";
GPIO8.associatedPins[2].pin.$assign = "PA12";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 32;

TIMER1.$name              = "TIMER_0";
TIMER1.timerStartTimer    = true;
TIMER1.timerClkPrescale   = 200;
TIMER1.interrupts         = ["ZERO"];
TIMER1.interruptPriority  = "0";
TIMER1.timerClkDiv        = 8;
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "5 ms";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.$suggestSolution           = "ADC0";
ADC121.peripheral.adcPin0.$suggestSolution   = "PA27";
ADC122.peripheral.$suggestSolution           = "ADC1";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution = "PB2";
GPIO6.associatedPins[0].pin.$suggestSolution = "PA13";
GPIO7.associatedPins[0].pin.$suggestSolution = "PA26";
GPIO7.associatedPins[1].pin.$suggestSolution = "PA25";
