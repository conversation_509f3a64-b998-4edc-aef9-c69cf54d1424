Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers to board.o(.text.board_init) for board_init
    empty.o(.text.main) refers to bsp_mpu6050.o(.text.MPU6050_Init) for MPU6050_Init
    empty.o(.text.main) refers to puts.o(.text) for puts
    empty.o(.text.main) refers to inv_mpu.o(.text.mpu_dmp_init) for mpu_dmp_init
    empty.o(.text.main) refers to board.o(.text.delay_ms) for delay_ms
    empty.o(.text.main) refers to inv_mpu.o(.text.mpu_dmp_get_data) for mpu_dmp_get_data
    empty.o(.text.main) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.main) refers to noretval__2printf.o(.text) for __2printf
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to board.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    board.o(.text.board_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    board.o(.text.board_init) refers to puts.o(.text) for puts
    board.o(.ARM.exidx.text.board_init) refers to board.o(.text.board_init) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_us) refers to board.o(.text.delay_us) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_ms) refers to board.o(.text.delay_ms) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_1us) refers to board.o(.text.delay_1us) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_1ms) refers to board.o(.text.delay_1ms) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.uart0_send_char) refers to board.o(.text.uart0_send_char) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.uart0_send_string) refers to board.o(.text.uart0_send_string) for [Anonymous Symbol]
    board.o(.ARM.exidx.text._sys_exit) refers to board.o(.text._sys_exit) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.fputc) refers to board.o(.text.fputc) for [Anonymous Symbol]
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_length) for recv0_length
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_buff) for recv0_buff
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_flag) for recv0_flag
    board.o(.ARM.exidx.text.UART0_IRQHandler) refers to board.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    bsp_mpu6050.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Start) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers to bsp_mpu6050.o(.text.IIC_Start) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Stop) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers to bsp_mpu6050.o(.text.IIC_Stop) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.IIC_Send_Ack) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.I2C_WaitAck) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.Send_Byte) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers to bsp_mpu6050.o(.text.Send_Byte) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.Read_Byte) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers to bsp_mpu6050.o(.text.Read_Byte) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.Send_Byte) for Send_Byte
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.Send_Byte) for Send_Byte
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.Read_Byte) for Read_Byte
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers to bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers to bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers to bsp_mpu6050.o(.text.MPU_Set_LPF) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU_Set_Rate) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers to bsp_mpu6050.o(.text.MPU6050ReadGyro) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers to bsp_mpu6050.o(.text.MPU6050ReadAcc) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to dflti.o(.text) for __aeabi_i2d
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to daddsub.o(.text) for __aeabi_dadd
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to d2f.o(.text) for __aeabi_d2f
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers to bsp_mpu6050.o(.text.MPU6050_GetTemp) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to noretval__2printf.o(.text) for __2printf
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to bsp_mpu6050.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers to bsp_mpu6050.o(.text.MPU6050ReadID) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_Init) refers to board.o(.text.delay_ms) for delay_ms
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050_Init) refers to noretval__2printf.o(.text) for __2printf
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_Init) for [Anonymous Symbol]
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_reg_dump) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_reg_dump) refers to noretval__2printf.o(.text) for __2printf
    inv_mpu.o(.text.mpu_reg_dump) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers to inv_mpu.o(.text.mpu_reg_dump) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers to inv_mpu.o(.text.mpu_read_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_init) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_init) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_init) refers to puts.o(.text) for puts
    inv_mpu.o(.text.mpu_init) refers to noretval__2printf.o(.text) for __2printf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.rodata.str1.1) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers to inv_mpu.o(.text.mpu_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_lpf) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers to inv_mpu.o(.text.mpu_set_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_set_sample_rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_set_sample_rate) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_set_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_configure_fifo) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_configure_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_bypass) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_set_bypass) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers to inv_mpu.o(.text.mpu_set_bypass) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_sensors) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_sensors) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_set_sensors) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers to inv_mpu.o(.text.mpu_set_sensors) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_int_latched) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_int_latched) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers to inv_mpu.o(.text.mpu_set_int_latched) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers to inv_mpu.o(.text.mpu_get_gyro_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers to inv_mpu.o(.text.mget_ms) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_accel_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_accel_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers to inv_mpu.o(.text.mpu_get_accel_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_temperature) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_temperature) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_get_temperature) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(.text.mpu_get_temperature) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_get_temperature) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_get_temperature) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_get_temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.mpu_get_temperature) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers to inv_mpu.o(.text.mpu_get_temperature) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_set_accel_bias) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_accel_bias) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers to inv_mpu.o(.text.mpu_set_accel_bias) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_reset_fifo) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_reset_fifo) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_reset_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.text.mpu_get_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers to inv_mpu.o(.text.mpu_get_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers to inv_mpu.o(.text.mpu_get_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers to inv_mpu.o(.text.mpu_get_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers to inv_mpu.o(.text.mpu_set_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_gyro_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers to inv_mpu.o(.text.mpu_get_gyro_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_accel_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers to inv_mpu.o(.text.mpu_get_accel_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_fifo_config) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers to inv_mpu.o(.text.mpu_get_fifo_config) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_power_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers to inv_mpu.o(.text.mpu_get_power_state) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_int_status) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_int_status) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers to inv_mpu.o(.text.mpu_get_int_status) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_fifo) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_fifo) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_int_level) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers to inv_mpu.o(.text.mpu_set_int_level) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.get_st_biases) for get_st_biases
    inv_mpu.o(.text.mpu_run_self_test) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_run_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._feq) for __aeabi_fcmpeq
    inv_mpu.o(.text.mpu_run_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_run_self_test) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_dmp_state) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.get_st_biases) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.get_st_biases) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.get_st_biases) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(.text.get_st_biases) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers to inv_mpu.o(.text.get_st_biases) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_write_mem) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_write_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers to inv_mpu.o(.text.mpu_write_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers to inv_mpu.o(.text.mpu_read_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_load_firmware) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_load_firmware) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(.text.mpu_load_firmware) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers to inv_mpu.o(.text.mpu_get_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers to inv_mpu.o(.text.setup_compass) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers to inv_mpu.o(.text.mpu_get_compass_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers to inv_mpu.o(.text.mpu_get_compass_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_motion_interrupt) for [Anonymous Symbol]
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(.text.run_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.run_self_test) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.run_self_test) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(.text.run_self_test) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers to inv_mpu.o(.text.run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(.text.inv_orientation_matrix_to_scalar) for [Anonymous Symbol]
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers to inv_mpu.o(.text.inv_row_2_scale) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_init) for mpu_init
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_dmp_init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.data.gyro_orientation) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_dmp_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_dmp_get_data) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(.text.mpu_dmp_get_data) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_dmp_get_data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_dmp_get_data) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_dmp_get_data) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(.text.mpu_dmp_get_data) refers to asin.o(i.asin) for asin
    inv_mpu.o(.text.mpu_dmp_get_data) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(.text.mpu_dmp_get_data) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text.mpu_dmp_get_data) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(.text.mpu_dmp_get_data) refers to atan2.o(i.atan2) for atan2
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers to inv_mpu.o(.text.mpu_dmp_get_data) for [Anonymous Symbol]
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.data.st) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.data.st) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.data.st) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.data.st) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.data.st) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.data.st) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.reg) for reg
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.hw) for hw
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.test) for test
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.4) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.4) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fflti.o(.text) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.5) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.5) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.0) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.1) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.0) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.1) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for DL_FlashCTL_massErase
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for DL_FlashCTL_massEraseFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    llsdiv.o(.text) refers to lludiv.o(.text) for __aeabi_uldivmod
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to board.o(.bss.__stdout) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to board.o(.bss.__stdout) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    puts.o(.text) refers to board.o(.text.fputc) for fputc
    puts.o(.text) refers to board.o(.bss.__stdout) for __stdout
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to daddsub.o(.text) for __aeabi_dadd
    asin.o(i.asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.asin) refers to dscalbn.o(.text) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to board.o(.text.fputc) for fputc
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalbn.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalbn.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    dsqrt.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to board.o(.text._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to board.o(.text._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to board.o(.text._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing board.o(.text), (0 bytes).
    Removing board.o(.ARM.exidx.text.board_init), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing board.o(.text.delay_1us), (68 bytes).
    Removing board.o(.ARM.exidx.text.delay_1us), (8 bytes).
    Removing board.o(.text.delay_1ms), (72 bytes).
    Removing board.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing board.o(.text.uart0_send_char), (36 bytes).
    Removing board.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing board.o(.text.uart0_send_string), (56 bytes).
    Removing board.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing board.o(.ARM.exidx.text._sys_exit), (8 bytes).
    Removing board.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing board.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing bsp_mpu6050.o(.text), (0 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Start), (56 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Stop), (52 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Send_Ack), (68 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack), (8 bytes).
    Removing bsp_mpu6050.o(.text.I2C_WaitAck), (236 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck), (8 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.Send_Byte), (8 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.Read_Byte), (8 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg), (8 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr), (16 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr), (16 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_LPF), (76 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Rate), (120 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadGyro), (50 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadAcc), (50 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_GetTemp), (68 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadID), (76 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID), (8 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init), (8 bytes).
    Removing inv_mpu.o(.text), (0 bytes).
    Removing inv_mpu.o(.text.mpu_reg_dump), (112 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reg_dump), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_reg), (48 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_reg), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_init), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_gyro_fsr), (136 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_lpf), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_bypass), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sensors), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_latched), (108 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_reg), (72 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mget_ms), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_reg), (72 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_temperature), (120 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_temperature), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_accel_bias), (232 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_fsr), (36 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_lpf), (40 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_lpf), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_sample_rate), (28 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_compass_sample_rate), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_sens), (72 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_sens), (64 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_power_state), (20 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_power_state), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_int_status), (56 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_int_status), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_fifo), (360 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_level), (8 bytes).
    Removing inv_mpu.o(.text.mpu_run_self_test), (2104 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_run_self_test), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.get_st_biases), (808 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.get_st_biases), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_write_mem), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_mem), (100 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_mem), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_load_firmware), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_dmp_state), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.setup_compass), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.setup_compass), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_reg), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_fsr), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_lp_motion_interrupt), (900 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt), (8 bytes).
    Removing inv_mpu.o(.text.run_self_test), (228 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.run_self_test), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar), (8 bytes).
    Removing inv_mpu.o(.text.inv_row_2_scale), (56 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.inv_row_2_scale), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_dmp_init), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data), (8 bytes).
    Removing inv_mpu.o(.rodata.cst8), (8 bytes).
    Removing inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.8), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text), (0 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias), (236 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias), (200 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh), (46 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count), (34 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time), (40 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal), (68 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode), (84 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_fifo_rate.regs_end), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__unnamed_1), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__unnamed_2), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata.str1.4), (24 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_setClockConfig), (64 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (52 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (60 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (60 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (92 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (52 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (152 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (104 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (136 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (160 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (36 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.text.DL_DMA_initChannel), (68 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (22 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (116 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (264 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (192 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (196 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (148 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (444 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (92 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (12 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (92 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (172 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (224 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (140 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (204 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (164 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (204 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (120 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (240 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (128 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (132 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (40 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (212 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (536 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (264 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (292 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (220 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (128 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (56 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (228 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (276 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (52 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (48 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (144 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (124 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.text.DL_SPI_init), (68 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_setClockConfig), (18 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (116 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (116 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.text.DL_Timer_setClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initTimerMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (268 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (228 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (172 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (96 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initFourCCPWMMode), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (44 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (72 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (68 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (48 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (36 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (32 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (18 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (16 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).

795 unused section(s) (total 30678 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  puts.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/dsqrt.c                  0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  dscalbn.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    board.c                                  0x00000000   Number         0  board.o ABSOLUTE
    bsp_mpu6050.c                            0x00000000   Number         0  bsp_mpu6050.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    inv_mpu.c                                0x00000000   Number         0  inv_mpu.o ABSOLUTE
    inv_mpu_dmp_motion_driver.c              0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000164   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x00000166   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x00000170   Section       10  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000017  0x0000017a   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0000017e   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000180   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000182   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x00000184   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x00000186   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x00000186   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x00000186   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0000018c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0000018c   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000190   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000190   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x00000198   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0000019a   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0000019a   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0000019e   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x000001a4   Section       44  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001d0   Section        0  noretval__2printf.o(.text)
    .text                                    0x000001ec   Section        0  _printf_pad.o(.text)
    .text                                    0x0000023c   Section        0  _printf_dec.o(.text)
    .text                                    0x000002a8   Section        0  _printf_hex_int.o(.text)
    .text                                    0x00000300   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x00000438   Section        0  puts.o(.text)
    .text                                    0x00000464   Section        0  memcmp.o(.text)
    .text                                    0x000004be   Section        0  heapauxi.o(.text)
    .text                                    0x000004c4   Section        0  d2f.o(.text)
    _dadd1                                   0x00000541   Thumb Code   290  daddsub.o(.text)
    .text                                    0x00000540   Section        0  daddsub.o(.text)
    _dsub1                                   0x00000663   Thumb Code   470  daddsub.o(.text)
    .text                                    0x00000898   Section        0  ddiv.o(.text)
    .text                                    0x00000ce0   Section        0  dmul.o(.text)
    .text                                    0x00000f28   Section        0  f2d.o(.text)
    .text                                    0x00000f7c   Section        0  fdiv.o(.text)
    .text                                    0x000010dc   Section        0  ffixi.o(.text)
    .text                                    0x00001128   Section        0  fflti.o(.text)
    .text                                    0x00001186   Section        0  _rserrno.o(.text)
    .text                                    0x0000119c   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0000124c   Section        0  _printf_char_file.o(.text)
    .text                                    0x00001274   Section        0  rtudiv10.o(.text)
    .text                                    0x0000129c   Section        0  dscalbn.o(.text)
    .text                                    0x000012fc   Section        8  rt_errno_addr_intlibspace.o(.text)
    _printf_input_char                       0x00001305   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x00001304   Section        0  _printf_char_common.o(.text)
    .text                                    0x00001334   Section        0  ferror.o(.text)
    .text                                    0x0000133c   Section        0  dcmpin.o(.text)
    .text                                    0x000013dc   Section        0  dsqrt.o(.text)
    .text                                    0x000014dc   Section        8  libspace.o(.text)
    .text                                    0x000014e4   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00001522   Section        0  exit.o(.text)
    .text                                    0x00001532   Section        0  cmpret.o(.text)
    .text                                    0x00001560   Section        0  dnan2.o(.text)
    .text                                    0x00001574   Section        0  retnan.o(.text)
    [Anonymous Symbol]                       0x000015d2   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x000015dc   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x0000161c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001620   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00001624   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00001638   Section        0  bsp_mpu6050.o(.text.MPU6050_Init)
    __arm_cp.16_0                            0x00001754   Number         4  bsp_mpu6050.o(.text.MPU6050_Init)
    __arm_cp.16_1                            0x00001758   Number         4  bsp_mpu6050.o(.text.MPU6050_Init)
    __arm_cp.16_2                            0x0000175c   Number         4  bsp_mpu6050.o(.text.MPU6050_Init)
    __arm_cp.16_3                            0x00001760   Number         4  bsp_mpu6050.o(.text.MPU6050_Init)
    __arm_cp.16_4                            0x00001764   Number         4  bsp_mpu6050.o(.text.MPU6050_Init)
    [Anonymous Symbol]                       0x00001768   Section        0  bsp_mpu6050.o(.text.MPU6050_ReadData)
    __arm_cp.7_0                             0x00001adc   Number         4  bsp_mpu6050.o(.text.MPU6050_ReadData)
    __arm_cp.7_1                             0x00001ae0   Number         4  bsp_mpu6050.o(.text.MPU6050_ReadData)
    __arm_cp.7_2                             0x00001ae4   Number         4  bsp_mpu6050.o(.text.MPU6050_ReadData)
    __arm_cp.7_3                             0x00001ae8   Number         4  bsp_mpu6050.o(.text.MPU6050_ReadData)
    [Anonymous Symbol]                       0x00001aec   Section        0  bsp_mpu6050.o(.text.MPU6050_WriteReg)
    __arm_cp.6_0                             0x00001e24   Number         4  bsp_mpu6050.o(.text.MPU6050_WriteReg)
    __arm_cp.6_1                             0x00001e28   Number         4  bsp_mpu6050.o(.text.MPU6050_WriteReg)
    __arm_cp.6_2                             0x00001e2c   Number         4  bsp_mpu6050.o(.text.MPU6050_WriteReg)
    __arm_cp.6_3                             0x00001e30   Number         4  bsp_mpu6050.o(.text.MPU6050_WriteReg)
    [Anonymous Symbol]                       0x00001e34   Section        0  bsp_mpu6050.o(.text.Read_Byte)
    __arm_cp.5_0                             0x00001f70   Number         4  bsp_mpu6050.o(.text.Read_Byte)
    __arm_cp.5_1                             0x00001f74   Number         4  bsp_mpu6050.o(.text.Read_Byte)
    __arm_cp.5_2                             0x00001f78   Number         4  bsp_mpu6050.o(.text.Read_Byte)
    __arm_cp.5_3                             0x00001f7c   Number         4  bsp_mpu6050.o(.text.Read_Byte)
    [Anonymous Symbol]                       0x00001f80   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001fcc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00001fd0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00001fd4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001fd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00001fdc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00001fe0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00001fe4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001fe8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x0000201c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00002020   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00002024   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.5_0                             0x0000203c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00002040   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_0                             0x00002094   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_1                             0x00002098   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_2                             0x0000209c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_3                             0x000020a0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_4                             0x000020a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_5                             0x000020a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x000020ac   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x000020c4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000020e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000020e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000020ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000020f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x000020f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x000020f8   Section        0  bsp_mpu6050.o(.text.Send_Byte)
    __arm_cp.4_0                             0x00002234   Number         4  bsp_mpu6050.o(.text.Send_Byte)
    __arm_cp.4_1                             0x00002238   Number         4  bsp_mpu6050.o(.text.Send_Byte)
    [Anonymous Symbol]                       0x0000223c   Section        0  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_0                             0x00002288   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_1                             0x0000228c   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_2                             0x00002290   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_3                             0x00002294   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_4                             0x00002298   Number         4  board.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x0000229c   Section        0  board.o(.text._sys_exit)
    [Anonymous Symbol]                       0x000022a0   Section        0  board.o(.text.board_init)
    __arm_cp.0_0                             0x000022bc   Number         4  board.o(.text.board_init)
    __arm_cp.0_1                             0x000022c0   Number         4  board.o(.text.board_init)
    [Anonymous Symbol]                       0x000022e4   Section        0  board.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00002328   Section        0  board.o(.text.delay_us)
    __arm_cp.1_0                             0x00002368   Number         4  board.o(.text.delay_us)
    [Anonymous Symbol]                       0x0000236c   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_0                            0x000025c8   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_1                            0x000025cc   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_2                            0x000025d0   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_3                            0x000025d4   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_4                            0x000025d8   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_5                            0x000025dc   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_6                            0x000025e0   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_7                            0x000025e4   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_8                            0x000025e8   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_9                            0x000025ec   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    [Anonymous Symbol]                       0x000025f0   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    __arm_cp.0_0                             0x00002604   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    __arm_cp.0_1                             0x00002608   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    [Anonymous Symbol]                       0x0000260c   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_0                            0x000027ac   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_1                            0x000027b0   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_2                            0x000027b4   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_3                            0x000027b8   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    [Anonymous Symbol]                       0x000027bc   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_0                             0x00002810   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_1                             0x00002814   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_2                             0x00002818   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_3                             0x0000281c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_4                             0x00002820   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_5                             0x00002824   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    [Anonymous Symbol]                       0x00002828   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_0                             0x000028e8   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_1                             0x000028ec   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_2                             0x000028f0   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_3                             0x000028f4   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_4                             0x000028f8   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_5                             0x000028fc   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    [Anonymous Symbol]                       0x00002900   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_0                             0x00002a20   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_1                             0x00002a24   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_2                             0x00002a28   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_3                             0x00002a2c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_4                             0x00002a30   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    [Anonymous Symbol]                       0x00002a34   Section        0  board.o(.text.fputc)
    __arm_cp.8_0                             0x00002a54   Number         4  board.o(.text.fputc)
    [Anonymous Symbol]                       0x00002a58   Section        0  inv_mpu.o(.text.inv_orientation_matrix_to_scalar)
    [Anonymous Symbol]                       0x00002b14   Section        0  empty.o(.text.main)
    [Anonymous Symbol]                       0x00002bf8   Section        0  inv_mpu.o(.text.mget_ms)
    [Anonymous Symbol]                       0x00002bfc   Section        0  inv_mpu.o(.text.mpu_configure_fifo)
    [Anonymous Symbol]                       0x00002c94   Section        0  inv_mpu.o(.text.mpu_dmp_get_data)
    __arm_cp.47_0                            0x00002e54   Number         4  inv_mpu.o(.text.mpu_dmp_get_data)
    __arm_cp.47_1                            0x00002e58   Number         4  inv_mpu.o(.text.mpu_dmp_get_data)
    [Anonymous Symbol]                       0x00002e5c   Section        0  inv_mpu.o(.text.mpu_dmp_init)
    __arm_cp.46_1                            0x00002f2c   Number         4  inv_mpu.o(.text.mpu_dmp_init)
    [Anonymous Symbol]                       0x00002f30   Section        0  inv_mpu.o(.text.mpu_get_accel_fsr)
    __arm_cp.19_0                            0x00002f58   Number         4  inv_mpu.o(.text.mpu_get_accel_fsr)
    __arm_cp.19_1                            0x00002f5c   Number         4  inv_mpu.o(.text.mpu_get_accel_fsr)
    __arm_cp.19_2                            0x00002f60   Number         4  inv_mpu.o(.text.mpu_get_accel_fsr)
    [Anonymous Symbol]                       0x00002f64   Section        0  inv_mpu.o(.text.mpu_init)
    __arm_cp.2_0                             0x000030a8   Number         4  inv_mpu.o(.text.mpu_init)
    __arm_cp.2_2                             0x000030cc   Number         4  inv_mpu.o(.text.mpu_init)
    __arm_cp.2_3                             0x000030d0   Number         4  inv_mpu.o(.text.mpu_init)
    [Anonymous Symbol]                       0x000030fc   Section        0  inv_mpu.o(.text.mpu_load_firmware)
    __arm_cp.37_0                            0x00003220   Number         4  inv_mpu.o(.text.mpu_load_firmware)
    [Anonymous Symbol]                       0x00003224   Section        0  inv_mpu.o(.text.mpu_lp_accel_mode)
    [Anonymous Symbol]                       0x00003424   Section        0  inv_mpu.o(.text.mpu_read_fifo_stream)
    __arm_cp.30_0                            0x000034c4   Number         4  inv_mpu.o(.text.mpu_read_fifo_stream)
    [Anonymous Symbol]                       0x000034c8   Section        0  inv_mpu.o(.text.mpu_reset_fifo)
    __arm_cp.17_0                            0x00003610   Number         4  inv_mpu.o(.text.mpu_reset_fifo)
    [Anonymous Symbol]                       0x00003614   Section        0  inv_mpu.o(.text.mpu_set_accel_fsr)
    [Anonymous Symbol]                       0x00003690   Section        0  inv_mpu.o(.text.mpu_set_bypass)
    __arm_cp.8_0                             0x00003784   Number         4  inv_mpu.o(.text.mpu_set_bypass)
    [Anonymous Symbol]                       0x00003788   Section        0  inv_mpu.o(.text.mpu_set_dmp_state)
    [Anonymous Symbol]                       0x00003854   Section        0  inv_mpu.o(.text.mpu_set_lpf)
    __arm_cp.5_0                             0x000038c4   Number         4  inv_mpu.o(.text.mpu_set_lpf)
    [Anonymous Symbol]                       0x000038c8   Section        0  inv_mpu.o(.text.mpu_set_sample_rate)
    __arm_cp.6_0                             0x000039a8   Number         4  inv_mpu.o(.text.mpu_set_sample_rate)
    [Anonymous Symbol]                       0x000039ac   Section        0  inv_mpu.o(.text.mpu_set_sensors)
    __arm_cp.9_0                             0x00003a84   Number         4  inv_mpu.o(.text.mpu_set_sensors)
    [Anonymous Symbol]                       0x00003a88   Section        0  inv_mpu.o(.text.mpu_write_mem)
    __arm_cp.35_0                            0x00003ae8   Number         4  inv_mpu.o(.text.mpu_write_mem)
    .text_divfast                            0x00003aec   Section      502  aeabi_sdivfast.o(.text_divfast)
    i.__ARM_fpclassify                       0x00003ce4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x00003d10   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x00003dbc   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x00003dc6   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x00003dce   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x00003de0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x00003df4   Section        0  __printf_wp.o(i._is_digit)
    i.asin                                   0x00003e04   Section        0  asin.o(i.asin)
    i.atan                                   0x00004078   Section        0  atan.o(i.atan)
    i.atan2                                  0x00004294   Section        0  atan2.o(i.atan2)
    i.sqrt                                   0x00004430   Section        0  sqrt.o(i.sqrt)
    x$fpl$deqf                               0x00004478   Section      100  deqf.o(x$fpl$deqf)
    x$fpl$fadd                               0x000044dc   Section      140  faddsub.o(x$fpl$fadd)
    _fadd1                                   0x000044e9   Thumb Code     0  faddsub.o(x$fpl$fadd)
    x$fpl$fmul                               0x00004568   Section      176  fmul.o(x$fpl$fmul)
    x$fpl$fsub                               0x00004618   Section      208  faddsub.o(x$fpl$fsub)
    _fsub1                                   0x00004625   Thumb Code     0  faddsub.o(x$fpl$fsub)
    uc_hextab                                0x000046e8   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x000046e8   Section       40  _printf_hex_int.o(.constdata)
    x$fpl$usenofp                            0x000046e8   Section        0  usenofp.o(x$fpl$usenofp)
    lc_hextab                                0x000046fc   Data          20  _printf_hex_int.o(.constdata)
    maptable                                 0x00004710   Data          17  __printf_flags_wp.o(.constdata)
    .constdata                               0x00004710   Section       17  __printf_flags_wp.o(.constdata)
    ddiv_reciptbl                            0x00004721   Data         128  ddiv.o(.constdata)
    .constdata                               0x00004721   Section      128  ddiv.o(.constdata)
    fdiv_tab                                 0x000047a1   Data          64  fdiv.o(.constdata)
    .constdata                               0x000047a1   Section       64  fdiv.o(.constdata)
    pS                                       0x000047e8   Data          48  asin.o(.constdata)
    .constdata                               0x000047e8   Section       80  asin.o(.constdata)
    qS                                       0x00004818   Data          32  asin.o(.constdata)
    atanhi                                   0x00004838   Data          32  atan.o(.constdata)
    .constdata                               0x00004838   Section      152  atan.o(.constdata)
    atanlo                                   0x00004858   Data          32  atan.o(.constdata)
    aTodd                                    0x00004878   Data          40  atan.o(.constdata)
    aTeven                                   0x000048a0   Data          48  atan.o(.constdata)
    .constdata                               0x000048d0   Section        8  qnan.o(.constdata)
    [Anonymous Symbol]                       0x000048d8   Section        0  inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes)
    [Anonymous Symbol]                       0x000048db   Section        0  inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes)
    dmp_memory                               0x000048de   Data        3062  inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory)
    [Anonymous Symbol]                       0x000048de   Section        0  inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory)
    gUART_0ClockConfig                       0x000054d4   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000054d4   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000054d6   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000054d6   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00005507   Section        0  bsp_mpu6050.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x00005538   Section        0  inv_mpu.o(.rodata.str1.1)
    gyro_orientation                         0x20200000   Data           9  inv_mpu.o(.data.gyro_orientation)
    [Anonymous Symbol]                       0x20200000   Section        0  inv_mpu.o(.data.gyro_orientation)
    st                                       0x2020000c   Data          44  inv_mpu.o(.data.st)
    [Anonymous Symbol]                       0x2020000c   Section        0  inv_mpu.o(.data.st)
    .bss                                     0x20200038   Section       96  libspace.o(.bss)
    dmp.0                                    0x202000ec   Data           4  inv_mpu_dmp_motion_driver.o(.bss.dmp.0)
    [Anonymous Symbol]                       0x202000ec   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.0)
    dmp.1                                    0x202000f0   Data           4  inv_mpu_dmp_motion_driver.o(.bss.dmp.1)
    [Anonymous Symbol]                       0x202000f0   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.1)
    dmp.2                                    0x202000f4   Data           2  inv_mpu_dmp_motion_driver.o(.bss.dmp.2)
    [Anonymous Symbol]                       0x202000f4   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.2)
    dmp.3                                    0x202000f8   Data           2  inv_mpu_dmp_motion_driver.o(.bss.dmp.3)
    [Anonymous Symbol]                       0x202000f8   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.3)
    dmp.4                                    0x202000fc   Data           2  inv_mpu_dmp_motion_driver.o(.bss.dmp.4)
    [Anonymous Symbol]                       0x202000fc   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.4)
    dmp.5                                    0x202000fe   Data           1  inv_mpu_dmp_motion_driver.o(.bss.dmp.5)
    [Anonymous Symbol]                       0x202000fe   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.5)
    Heap_Mem                                 0x20200188   Data           0  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200188   Data         256  startup_mspm0g350x_uvision.o(STACK)
    HEAP                                     0x20200188   Section        0  startup_mspm0g350x_uvision.o(HEAP)
    STACK                                    0x20200188   Section      256  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20200288   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000165   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x00000167   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_x                                0x00000171   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_percent_end                      0x0000017b   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0000017f   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000183   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x00000187   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x00000187   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0000018d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0000018d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000191   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000191   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x00000199   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0000019b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0000019b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0000019f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x000001a5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000001a9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000001ab   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000001ad   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000001af   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000001b1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001b3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001b5   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __2printf                                0x000001d1   Thumb Code    22  noretval__2printf.o(.text)
    _printf_pre_padding                      0x000001ed   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x00000219   Thumb Code    34  _printf_pad.o(.text)
    _printf_int_dec                          0x0000023d   Thumb Code    90  _printf_dec.o(.text)
    _printf_int_hex                          0x000002a9   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x000002a9   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x00000301   Thumb Code   306  __printf_flags_wp.o(.text)
    puts                                     0x00000439   Thumb Code    38  puts.o(.text)
    memcmp                                   0x00000465   Thumb Code    90  memcmp.o(.text)
    __use_two_region_memory                  0x000004bf   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x000004c1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000004c3   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x000004c5   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x000004c5   Thumb Code   120  d2f.o(.text)
    __aeabi_dadd                             0x00000839   Thumb Code     0  daddsub.o(.text)
    _dadd                                    0x00000839   Thumb Code    26  daddsub.o(.text)
    __aeabi_dsub                             0x00000853   Thumb Code     0  daddsub.o(.text)
    _dsub                                    0x00000853   Thumb Code    22  daddsub.o(.text)
    __aeabi_drsub                            0x00000869   Thumb Code     0  daddsub.o(.text)
    _drsb                                    0x00000869   Thumb Code    28  daddsub.o(.text)
    __aeabi_ddiv                             0x00000899   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x00000899   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000cc9   Thumb Code    20  ddiv.o(.text)
    __aeabi_dmul                             0x00000ce1   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x00000ce1   Thumb Code   558  dmul.o(.text)
    __aeabi_f2d                              0x00000f29   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x00000f29   Thumb Code    80  f2d.o(.text)
    __aeabi_fdiv                             0x00000f7d   Thumb Code     0  fdiv.o(.text)
    _fdiv                                    0x00000f7d   Thumb Code   334  fdiv.o(.text)
    _frdiv                                   0x000010cb   Thumb Code     8  fdiv.o(.text)
    __aeabi_f2iz                             0x000010dd   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x000010dd   Thumb Code    76  ffixi.o(.text)
    __aeabi_i2f_normalise                    0x00001129   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x00001171   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x00001171   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x00001181   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x00001181   Thumb Code     0  fflti.o(.text)
    __read_errno                             0x00001187   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x00001191   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0000119d   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_char_file                        0x0000124d   Thumb Code    34  _printf_char_file.o(.text)
    __rt_udiv10                              0x00001275   Thumb Code    40  rtudiv10.o(.text)
    __ARM_scalbn                             0x0000129d   Thumb Code    84  dscalbn.o(.text)
    __aeabi_errno_addr                       0x000012fd   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x000012fd   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x000012fd   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x0000130f   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x00001335   Thumb Code     8  ferror.o(.text)
    __fpl_dcmp_InfNaN                        0x0000133d   Thumb Code   154  dcmpin.o(.text)
    _dsqrt                                   0x000013dd   Thumb Code   248  dsqrt.o(.text)
    __user_libspace                          0x000014dd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x000014dd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x000014dd   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x000014e5   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x00001523   Thumb Code    16  exit.o(.text)
    __fpl_cmpreturn                          0x00001533   Thumb Code    46  cmpret.o(.text)
    __fpl_dcheck_NaN2                        0x00001561   Thumb Code    14  dnan2.o(.text)
    __fpl_return_NaN                         0x00001575   Thumb Code    94  retnan.o(.text)
    DL_Common_delayCycles                    0x000015d3   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_UART_init                             0x000015dd   Thumb Code    64  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001625   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    MPU6050_Init                             0x00001639   Thumb Code   284  bsp_mpu6050.o(.text.MPU6050_Init)
    MPU6050_ReadData                         0x00001769   Thumb Code   884  bsp_mpu6050.o(.text.MPU6050_ReadData)
    MPU6050_WriteReg                         0x00001aed   Thumb Code   824  bsp_mpu6050.o(.text.MPU6050_WriteReg)
    Read_Byte                                0x00001e35   Thumb Code   316  bsp_mpu6050.o(.text.Read_Byte)
    SYSCFG_DL_GPIO_init                      0x00001f81   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_SYSCTL_init                    0x00001fe9   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00002025   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_UART_0_init                    0x00002041   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x000020ad   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x000020c5   Thumb Code    32  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Send_Byte                                0x000020f9   Thumb Code   316  bsp_mpu6050.o(.text.Send_Byte)
    UART0_IRQHandler                         0x0000223d   Thumb Code    76  board.o(.text.UART0_IRQHandler)
    _sys_exit                                0x0000229d   Thumb Code     2  board.o(.text._sys_exit)
    board_init                               0x000022a1   Thumb Code    28  board.o(.text.board_init)
    delay_ms                                 0x000022e5   Thumb Code    68  board.o(.text.delay_ms)
    delay_us                                 0x00002329   Thumb Code    64  board.o(.text.delay_us)
    dmp_enable_feature                       0x0000236d   Thumb Code   604  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    dmp_load_motion_driver_firmware          0x000025f1   Thumb Code    20  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x0000260d   Thumb Code   416  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    dmp_set_fifo_rate                        0x000027bd   Thumb Code    84  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    dmp_set_orientation                      0x00002829   Thumb Code   192  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    dmp_set_tap_thresh                       0x00002901   Thumb Code   288  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    fputc                                    0x00002a35   Thumb Code    32  board.o(.text.fputc)
    inv_orientation_matrix_to_scalar         0x00002a59   Thumb Code   188  inv_mpu.o(.text.inv_orientation_matrix_to_scalar)
    main                                     0x00002b15   Thumb Code   132  empty.o(.text.main)
    mget_ms                                  0x00002bf9   Thumb Code     2  inv_mpu.o(.text.mget_ms)
    mpu_configure_fifo                       0x00002bfd   Thumb Code   152  inv_mpu.o(.text.mpu_configure_fifo)
    mpu_dmp_get_data                         0x00002c95   Thumb Code   448  inv_mpu.o(.text.mpu_dmp_get_data)
    mpu_dmp_init                             0x00002e5d   Thumb Code   208  inv_mpu.o(.text.mpu_dmp_init)
    mpu_get_accel_fsr                        0x00002f31   Thumb Code    40  inv_mpu.o(.text.mpu_get_accel_fsr)
    mpu_init                                 0x00002f65   Thumb Code   324  inv_mpu.o(.text.mpu_init)
    mpu_load_firmware                        0x000030fd   Thumb Code   292  inv_mpu.o(.text.mpu_load_firmware)
    mpu_lp_accel_mode                        0x00003225   Thumb Code   512  inv_mpu.o(.text.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x00003425   Thumb Code   160  inv_mpu.o(.text.mpu_read_fifo_stream)
    mpu_reset_fifo                           0x000034c9   Thumb Code   328  inv_mpu.o(.text.mpu_reset_fifo)
    mpu_set_accel_fsr                        0x00003615   Thumb Code   124  inv_mpu.o(.text.mpu_set_accel_fsr)
    mpu_set_bypass                           0x00003691   Thumb Code   244  inv_mpu.o(.text.mpu_set_bypass)
    mpu_set_dmp_state                        0x00003789   Thumb Code   204  inv_mpu.o(.text.mpu_set_dmp_state)
    mpu_set_lpf                              0x00003855   Thumb Code   112  inv_mpu.o(.text.mpu_set_lpf)
    mpu_set_sample_rate                      0x000038c9   Thumb Code   224  inv_mpu.o(.text.mpu_set_sample_rate)
    mpu_set_sensors                          0x000039ad   Thumb Code   216  inv_mpu.o(.text.mpu_set_sensors)
    mpu_write_mem                            0x00003a89   Thumb Code    96  inv_mpu.o(.text.mpu_write_mem)
    __aeabi_uidiv                            0x00003aed   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x00003b31   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    __ARM_fpclassify                         0x00003ce5   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x00003d11   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x00003dbd   Thumb Code    10  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x00003dc7   Thumb Code     8  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x00003dcf   Thumb Code    16  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x00003de1   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x00003df5   Thumb Code    14  __printf_wp.o(i._is_digit)
    asin                                     0x00003e05   Thumb Code   574  asin.o(i.asin)
    atan                                     0x00004079   Thumb Code   472  atan.o(i.atan)
    atan2                                    0x00004295   Thumb Code   372  atan2.o(i.atan2)
    sqrt                                     0x00004431   Thumb Code    66  sqrt.o(i.sqrt)
    __aeabi_cdcmpeq                          0x00004479   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x00004479   Thumb Code    94  deqf.o(x$fpl$deqf)
    __aeabi_fadd                             0x000044dd   Thumb Code     0  faddsub.o(x$fpl$fadd)
    _fadd                                    0x000044dd   Thumb Code   134  faddsub.o(x$fpl$fadd)
    __aeabi_fmul                             0x00004569   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x00004569   Thumb Code   172  fmul.o(x$fpl$fmul)
    __aeabi_fsub                             0x00004619   Thumb Code     0  faddsub.o(x$fpl$fsub)
    _fsub                                    0x00004619   Thumb Code   204  faddsub.o(x$fpl$fsub)
    __I$use$fp                               0x000046e8   Number         0  usenofp.o(x$fpl$usenofp)
    __mathlib_zero                           0x000048d0   Data           8  qnan.o(.constdata)
    hw                                       0x000054e0   Data          12  inv_mpu.o(.rodata.hw)
    reg                                      0x000054ec   Data          27  inv_mpu.o(.rodata.reg)
    test                                     0x00005584   Data          40  inv_mpu.o(.rodata.test)
    Region$$Table$$Base                      0x000055b0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000055d0   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200038   Data          96  libspace.o(.bss)
    __stdout                                 0x20200098   Data          84  board.o(.bss.__stdout)
    __temporary_stack_top$libspace           0x20200098   Data           0  libspace.o(.bss)
    recv0_buff                               0x202000ff   Data         128  board.o(.bss.recv0_buff)
    recv0_flag                               0x2020017f   Data           1  board.o(.bss.recv0_flag)
    recv0_length                             0x20200180   Data           2  board.o(.bss.recv0_length)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00005608, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000055d0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           13    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO         1104  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1407    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO         1411    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO         1408    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO         1413    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO         1089    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000166   0x00000166   0x0000000a   Code   RO         1088    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x00000170   0x00000170   0x0000000a   Code   RO         1087    .ARM.Collect$$_printf_percent$$0000000C  c_p.l(_printf_x.o)
    0x0000017a   0x0000017a   0x00000004   Code   RO         1172    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x0000017e   0x0000017e   0x00000002   Code   RO         1267    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1284    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1286    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1288    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1291    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1293    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1295    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1298    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1300    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1302    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1304    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1306    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1308    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1310    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1312    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1314    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1316    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1318    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1322    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1324    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1326    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO         1328    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000002   Code   RO         1329    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000182   0x00000182   0x00000002   Code   RO         1359    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x00000184   0x00000184   0x00000000   Code   RO         1390    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO         1392    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO         1395    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO         1398    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO         1400    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO         1403    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000002   Code   RO         1404    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x00000186   0x00000186   0x00000000   Code   RO         1155    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x00000186   0x00000186   0x00000000   Code   RO         1215    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000006   Code   RO         1227    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x0000018c   0x0000018c   0x00000000   Code   RO         1217    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x0000018c   0x0000018c   0x00000004   Code   RO         1218    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000190   0x00000190   0x00000000   Code   RO         1220    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000190   0x00000190   0x00000008   Code   RO         1221    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x00000198   0x00000198   0x00000002   Code   RO         1275    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x0000019a   0x0000019a   0x00000000   Code   RO         1333    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x0000019a   0x0000019a   0x00000004   Code   RO         1334    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x0000019e   0x0000019e   0x00000006   Code   RO         1335    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x000001a4   0x000001a4   0x0000002c   Code   RO           14    .text               startup_mspm0g350x_uvision.o
    0x000001d0   0x000001d0   0x0000001c   Code   RO         1038    .text               c_p.l(noretval__2printf.o)
    0x000001ec   0x000001ec   0x0000004e   Code   RO         1042    .text               c_p.l(_printf_pad.o)
    0x0000023a   0x0000023a   0x00000002   PAD
    0x0000023c   0x0000023c   0x0000006c   Code   RO         1044    .text               c_p.l(_printf_dec.o)
    0x000002a8   0x000002a8   0x00000058   Code   RO         1049    .text               c_p.l(_printf_hex_int.o)
    0x00000300   0x00000300   0x00000138   Code   RO         1079    .text               c_p.l(__printf_flags_wp.o)
    0x00000438   0x00000438   0x0000002c   Code   RO         1090    .text               c_p.l(puts.o)
    0x00000464   0x00000464   0x0000005a   Code   RO         1092    .text               c_p.l(memcmp.o)
    0x000004be   0x000004be   0x00000006   Code   RO         1102    .text               c_p.l(heapauxi.o)
    0x000004c4   0x000004c4   0x0000007c   Code   RO         1106    .text               fz_ps.l(d2f.o)
    0x00000540   0x00000540   0x00000358   Code   RO         1108    .text               fz_ps.l(daddsub.o)
    0x00000898   0x00000898   0x00000448   Code   RO         1110    .text               fz_ps.l(ddiv.o)
    0x00000ce0   0x00000ce0   0x00000248   Code   RO         1115    .text               fz_ps.l(dmul.o)
    0x00000f28   0x00000f28   0x00000054   Code   RO         1117    .text               fz_ps.l(f2d.o)
    0x00000f7c   0x00000f7c   0x00000160   Code   RO         1137    .text               fz_ps.l(fdiv.o)
    0x000010dc   0x000010dc   0x0000004c   Code   RO         1140    .text               fz_ps.l(ffixi.o)
    0x00001128   0x00001128   0x0000005e   Code   RO         1142    .text               fz_ps.l(fflti.o)
    0x00001186   0x00001186   0x00000016   Code   RO         1166    .text               c_p.l(_rserrno.o)
    0x0000119c   0x0000119c   0x000000b0   Code   RO         1168    .text               c_p.l(_printf_intcommon.o)
    0x0000124c   0x0000124c   0x00000028   Code   RO         1170    .text               c_p.l(_printf_char_file.o)
    0x00001274   0x00001274   0x00000028   Code   RO         1173    .text               c_p.l(rtudiv10.o)
    0x0000129c   0x0000129c   0x00000060   Code   RO         1177    .text               fz_ps.l(dscalbn.o)
    0x000012fc   0x000012fc   0x00000008   Code   RO         1234    .text               c_p.l(rt_errno_addr_intlibspace.o)
    0x00001304   0x00001304   0x00000030   Code   RO         1236    .text               c_p.l(_printf_char_common.o)
    0x00001334   0x00001334   0x00000008   Code   RO         1238    .text               c_p.l(ferror.o)
    0x0000133c   0x0000133c   0x000000a0   Code   RO         1242    .text               fz_ps.l(dcmpin.o)
    0x000013dc   0x000013dc   0x00000100   Code   RO         1244    .text               fz_ps.l(dsqrt.o)
    0x000014dc   0x000014dc   0x00000008   Code   RO         1248    .text               c_p.l(libspace.o)
    0x000014e4   0x000014e4   0x0000003e   Code   RO         1251    .text               c_p.l(sys_stackheap_outer.o)
    0x00001522   0x00001522   0x00000010   Code   RO         1256    .text               c_p.l(exit.o)
    0x00001532   0x00001532   0x0000002e   Code   RO         1268    .text               fz_ps.l(cmpret.o)
    0x00001560   0x00001560   0x00000014   Code   RO         1270    .text               fz_ps.l(dnan2.o)
    0x00001574   0x00001574   0x0000005e   Code   RO         1330    .text               fz_ps.l(retnan.o)
    0x000015d2   0x000015d2   0x0000000a   Code   RO          354    .text.DL_Common_delayCycles  dl_common.o
    0x000015dc   0x000015dc   0x00000048   Code   RO          977    .text.DL_UART_init  dl_uart.o
    0x00001624   0x00001624   0x00000012   Code   RO          979    .text.DL_UART_setClockConfig  dl_uart.o
    0x00001636   0x00001636   0x00000002   PAD
    0x00001638   0x00001638   0x00000130   Code   RO          107    .text.MPU6050_Init  bsp_mpu6050.o
    0x00001768   0x00001768   0x00000384   Code   RO           89    .text.MPU6050_ReadData  bsp_mpu6050.o
    0x00001aec   0x00001aec   0x00000348   Code   RO           87    .text.MPU6050_WriteReg  bsp_mpu6050.o
    0x00001e34   0x00001e34   0x0000014c   Code   RO           85    .text.Read_Byte     bsp_mpu6050.o
    0x00001f80   0x00001f80   0x00000068   Code   RO           25    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001fe8   0x00001fe8   0x0000003c   Code   RO           27    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00002024   0x00002024   0x0000001c   Code   RO           31    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00002040   0x00002040   0x0000006c   Code   RO           29    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x000020ac   0x000020ac   0x00000018   Code   RO           21    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x000020c4   0x000020c4   0x00000034   Code   RO           23    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x000020f8   0x000020f8   0x00000144   Code   RO           83    .text.Send_Byte     bsp_mpu6050.o
    0x0000223c   0x0000223c   0x00000060   Code   RO           61    .text.UART0_IRQHandler  board.o
    0x0000229c   0x0000229c   0x00000002   Code   RO           57    .text._sys_exit     board.o
    0x0000229e   0x0000229e   0x00000002   PAD
    0x000022a0   0x000022a0   0x00000044   Code   RO           43    .text.board_init    board.o
    0x000022e4   0x000022e4   0x00000044   Code   RO           47    .text.delay_ms      board.o
    0x00002328   0x00002328   0x00000044   Code   RO           45    .text.delay_us      board.o
    0x0000236c   0x0000236c   0x00000284   Code   RO          266    .text.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x000025f0   0x000025f0   0x0000001c   Code   RO          230    .text.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x0000260c   0x0000260c   0x000001b0   Code   RO          278    .text.dmp_read_fifo  inv_mpu_dmp_motion_driver.o
    0x000027bc   0x000027bc   0x0000006c   Code   RO          238    .text.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x00002828   0x00002828   0x000000d8   Code   RO          232    .text.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x00002900   0x00002900   0x00000134   Code   RO          242    .text.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x00002a34   0x00002a34   0x00000024   Code   RO           59    .text.fputc         board.o
    0x00002a58   0x00002a58   0x000000bc   Code   RO          206    .text.inv_orientation_matrix_to_scalar  inv_mpu.o
    0x00002b14   0x00002b14   0x000000e4   Code   RO            2    .text.main          empty.o
    0x00002bf8   0x00002bf8   0x00000002   Code   RO          144    .text.mget_ms       inv_mpu.o
    0x00002bfa   0x00002bfa   0x00000002   PAD
    0x00002bfc   0x00002bfc   0x00000098   Code   RO          132    .text.mpu_configure_fifo  inv_mpu.o
    0x00002c94   0x00002c94   0x000001c8   Code   RO          212    .text.mpu_dmp_get_data  inv_mpu.o
    0x00002e5c   0x00002e5c   0x000000d4   Code   RO          210    .text.mpu_dmp_init  inv_mpu.o
    0x00002f30   0x00002f30   0x00000034   Code   RO          156    .text.mpu_get_accel_fsr  inv_mpu.o
    0x00002f64   0x00002f64   0x00000198   Code   RO          122    .text.mpu_init      inv_mpu.o
    0x000030fc   0x000030fc   0x00000128   Code   RO          192    .text.mpu_load_firmware  inv_mpu.o
    0x00003224   0x00003224   0x00000200   Code   RO          138    .text.mpu_lp_accel_mode  inv_mpu.o
    0x00003424   0x00003424   0x000000a4   Code   RO          178    .text.mpu_read_fifo_stream  inv_mpu.o
    0x000034c8   0x000034c8   0x0000014c   Code   RO          152    .text.mpu_reset_fifo  inv_mpu.o
    0x00003614   0x00003614   0x0000007c   Code   RO          126    .text.mpu_set_accel_fsr  inv_mpu.o
    0x00003690   0x00003690   0x000000f8   Code   RO          134    .text.mpu_set_bypass  inv_mpu.o
    0x00003788   0x00003788   0x000000cc   Code   RO          184    .text.mpu_set_dmp_state  inv_mpu.o
    0x00003854   0x00003854   0x00000074   Code   RO          128    .text.mpu_set_lpf   inv_mpu.o
    0x000038c8   0x000038c8   0x000000e4   Code   RO          130    .text.mpu_set_sample_rate  inv_mpu.o
    0x000039ac   0x000039ac   0x000000dc   Code   RO          136    .text.mpu_set_sensors  inv_mpu.o
    0x00003a88   0x00003a88   0x00000064   Code   RO          188    .text.mpu_write_mem  inv_mpu.o
    0x00003aec   0x00003aec   0x000001f6   Code   RO         1095    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x00003ce2   0x00003ce2   0x00000002   PAD
    0x00003ce4   0x00003ce4   0x0000002c   Code   RO         1205    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00003d10   0x00003d10   0x000000ac   Code   RO         1207    i.__kernel_poly     m_ps.l(poly.o)
    0x00003dbc   0x00003dbc   0x0000000a   Code   RO         1192    i.__mathlib_dbl_infnan  m_ps.l(dunder.o)
    0x00003dc6   0x00003dc6   0x00000008   Code   RO         1193    i.__mathlib_dbl_infnan2  m_ps.l(dunder.o)
    0x00003dce   0x00003dce   0x00000010   Code   RO         1194    i.__mathlib_dbl_invalid  m_ps.l(dunder.o)
    0x00003dde   0x00003dde   0x00000002   PAD
    0x00003de0   0x00003de0   0x00000014   Code   RO         1197    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x00003df4   0x00003df4   0x0000000e   Code   RO         1077    i._is_digit         c_p.l(__printf_wp.o)
    0x00003e02   0x00003e02   0x00000002   PAD
    0x00003e04   0x00003e04   0x00000274   Code   RO         1147    i.asin              m_ps.l(asin.o)
    0x00004078   0x00004078   0x0000021c   Code   RO         1187    i.atan              m_ps.l(atan.o)
    0x00004294   0x00004294   0x0000019c   Code   RO         1152    i.atan2             m_ps.l(atan2.o)
    0x00004430   0x00004430   0x00000048   Code   RO         1211    i.sqrt              m_ps.l(sqrt.o)
    0x00004478   0x00004478   0x00000064   Code   RO         1175    x$fpl$deqf          fz_ps.l(deqf.o)
    0x000044dc   0x000044dc   0x0000008c   Code   RO         1119    x$fpl$fadd          fz_ps.l(faddsub.o)
    0x00004568   0x00004568   0x000000b0   Code   RO         1144    x$fpl$fmul          fz_ps.l(fmul.o)
    0x00004618   0x00004618   0x000000d0   Code   RO         1121    x$fpl$fsub          fz_ps.l(faddsub.o)
    0x000046e8   0x000046e8   0x00000000   Code   RO         1185    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x000046e8   0x000046e8   0x00000028   Data   RO         1050    .constdata          c_p.l(_printf_hex_int.o)
    0x00004710   0x00004710   0x00000011   Data   RO         1080    .constdata          c_p.l(__printf_flags_wp.o)
    0x00004721   0x00004721   0x00000080   Data   RO         1111    .constdata          fz_ps.l(ddiv.o)
    0x000047a1   0x000047a1   0x00000040   Data   RO         1138    .constdata          fz_ps.l(fdiv.o)
    0x000047e1   0x000047e1   0x00000007   PAD
    0x000047e8   0x000047e8   0x00000050   Data   RO         1148    .constdata          m_ps.l(asin.o)
    0x00004838   0x00004838   0x00000098   Data   RO         1188    .constdata          m_ps.l(atan.o)
    0x000048d0   0x000048d0   0x00000008   Data   RO         1209    .constdata          m_ps.l(qnan.o)
    0x000048d8   0x000048d8   0x00000003   Data   RO          286    .rodata..L__const.dmp_set_orientation.accel_axes  inv_mpu_dmp_motion_driver.o
    0x000048db   0x000048db   0x00000003   Data   RO          285    .rodata..L__const.dmp_set_orientation.gyro_axes  inv_mpu_dmp_motion_driver.o
    0x000048de   0x000048de   0x00000bf6   Data   RO          284    .rodata.dmp_memory  inv_mpu_dmp_motion_driver.o
    0x000054d4   0x000054d4   0x00000002   Data   RO           33    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000054d6   0x000054d6   0x0000000a   Data   RO           34    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000054e0   0x000054e0   0x0000000c   Data   RO          215    .rodata.hw          inv_mpu.o
    0x000054ec   0x000054ec   0x0000001b   Data   RO          214    .rodata.reg         inv_mpu.o
    0x00005507   0x00005507   0x00000031   Data   RO          109    .rodata.str1.1      bsp_mpu6050.o
    0x00005538   0x00005538   0x0000004c   Data   RO          219    .rodata.str1.1      inv_mpu.o
    0x00005584   0x00005584   0x00000028   Data   RO          216    .rodata.test        inv_mpu.o
    0x000055ac   0x000055ac   0x00000004   PAD
    0x000055b0   0x000055b0   0x00000020   Data   RO         1406    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000055d0, Size: 0x00000288, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x000055d0   0x00000009   Data   RW          218    .data.gyro_orientation  inv_mpu.o
    0x20200009   0x000055d9   0x00000003   PAD
    0x2020000c   0x000055dc   0x0000002c   Data   RW          217    .data.st            inv_mpu.o
    0x20200038        -       0x00000060   Zero   RW         1249    .bss                c_p.l(libspace.o)
    0x20200098        -       0x00000054   Zero   RW           66    .bss.__stdout       board.o
    0x202000ec        -       0x00000004   Zero   RW          287    .bss.dmp.0          inv_mpu_dmp_motion_driver.o
    0x202000f0        -       0x00000004   Zero   RW          288    .bss.dmp.1          inv_mpu_dmp_motion_driver.o
    0x202000f4        -       0x00000002   Zero   RW          289    .bss.dmp.2          inv_mpu_dmp_motion_driver.o
    0x202000f6   0x00005608   0x00000002   PAD
    0x202000f8        -       0x00000002   Zero   RW          290    .bss.dmp.3          inv_mpu_dmp_motion_driver.o
    0x202000fa   0x00005608   0x00000002   PAD
    0x202000fc        -       0x00000002   Zero   RW          291    .bss.dmp.4          inv_mpu_dmp_motion_driver.o
    0x202000fe        -       0x00000001   Zero   RW          292    .bss.dmp.5          inv_mpu_dmp_motion_driver.o
    0x202000ff        -       0x00000080   Zero   RW           63    .bss.recv0_buff     board.o
    0x2020017f        -       0x00000001   Zero   RW           65    .bss.recv0_flag     board.o
    0x20200180        -       0x00000002   Zero   RW           64    .bss.recv0_length   board.o
    0x20200182   0x00005608   0x00000006   PAD
    0x20200188        -       0x00000000   Zero   RW           12    HEAP                startup_mspm0g350x_uvision.o
    0x20200188        -       0x00000100   Zero   RW           11    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       338         68          0          0        215       9475   board.o
      2700         76         49          0          0      28139   bsp_mpu6050.o
        10          0          0          0          0        565   dl_common.o
        90          8          0          0          0      14490   dl_uart.o
       228         96          0          0          0        919   empty.o
      4014        148        155         53          0      30621   inv_mpu.o
      1736        140       3068          0         15      11802   inv_mpu_dmp_motion_driver.o
        44         18        192          0        256        668   startup_mspm0g350x_uvision.o
       376         84         12          0          0      20558   ti_msp_dl_config.o

    ----------------------------------------------------------------------
      9542        <USER>       <GROUP>         56        496     117237   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          4          3         10          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       312          6         17          0          0         76   __printf_flags_wp.o
        14          0          0          0          0         60   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         88   _printf_char_common.o
        40          6          0          0          0         72   _printf_char_file.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
        88          4         40          0          0         72   _printf_hex_int.o
       176          0          0          0          0         84   _printf_intcommon.o
        78          0          0          0          0        100   _printf_pad.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0          0   _printf_x.o
        22          0          0          0          0         92   _rserrno.o
       502          0          0          0          0         92   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         8          0          0          0          0         60   ferror.o
         6          0          0          0          0        136   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        90          0          0          0          0         68   memcmp.o
        28          6          0          0          0         84   noretval__2printf.o
        44          6          0          0          0         68   puts.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        62          0          0          0          0         80   sys_stackheap_outer.o
        46          0          0          0          0         60   cmpret.o
       124          4          0          0          0         72   d2f.o
       856         20          0          0          0        208   daddsub.o
       160          6          0          0          0         76   dcmpin.o
      1096         26        128          0          0        112   ddiv.o
       100          4          0          0          0         92   deqf.o
       584         26          0          0          0         84   dmul.o
        20          6          0          0          0         68   dnan2.o
        96         12          0          0          0         72   dscalbn.o
       256          8          0          0          0         76   dsqrt.o
        84          4          0          0          0         60   f2d.o
       348          8          0          0          0        160   faddsub.o
       352         10         64          0          0         92   fdiv.o
        76          0          0          0          0         68   ffixi.o
        94          0          0          0          0         92   fflti.o
       176          4          0          0          0         80   fmul.o
        94          0          0          0          0         68   retnan.o
         0          0          0          0          0          0   usenofp.o
       628         54         80          0          0        104   asin.o
       540         68        152          0          0        112   atan.o
       412         40          0          0          0        144   atan2.o
        54          6          0          0          0        272   dunder.o
        44          4          0          0          0         60   fpclassify.o
       172          0          0          0          0         76   poly.o
         0          0          8          0          0          0   qnan.o
        72          6          0          0          0         76   sqrt.o

    ----------------------------------------------------------------------
      8418        <USER>        <GROUP>          0         96       4024   Library Totals
        24          4          7          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1910         70         57          0         96       1640   c_p.l
      4562        138        192          0          0       1540   fz_ps.l
      1922        178        240          0          0        844   m_ps.l

    ----------------------------------------------------------------------
      8418        <USER>        <GROUP>          0         96       4024   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     17960       1028       4008         56        592     119209   Grand Totals
     17960       1028       4008         56        592     119209   ELF Image Totals
     17960       1028       4008         56          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                21968 (  21.45kB)
    Total RW  Size (RW Data + ZI Data)               648 (   0.63kB)
    Total ROM Size (Code + RO Data + RW Data)      22024 (  21.51kB)

==============================================================================

