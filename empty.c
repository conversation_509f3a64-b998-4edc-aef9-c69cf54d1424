#include "board.h"
#include <stdio.h>
#include "bsp_mpu6050.h"
#include "inv_mpu.h"
#include "drv_oled.h"
#include "ALLHeader.h"
#define DELAY (3200000)
uint32_t cnt=0;
unsigned char str[20]={0};
void OLED_Proc(void);
int main(void)
{
    //�������ʼ�� Development board initialization
    board_init();
    oled_init();//oled��ʾ����ʼ��
//    MPU6050_Init();
    // NVIC_EnableIRQ(AO_INST_INT_IRQN);//CCD ADC中断已禁用 CCD ADC interrupt disabled

    NVIC_EnableIRQ(ADC1_INST_INT_IRQN);//����ADC�ж� Enable ADC interrupt   HUIDU huidu
	    //�����ʱ���жϱ�־ Clear the timer interrupt flag
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    //ʹ�ܶ�ʱ���ж� Enable timer interrupt
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
	
    uint8_t ret = 1;
          
    float pitch=0,roll=0,yaw=0;   //ŷ���� Euler Angles

    printf("start\r\n");

	Kp=30;
	Kd=200;
    while(1)
    {
			    
			     
//          deal_data_ccd(); // CCD数据处理已移除
			    
		OLED_Proc();
		
//					display_6_8_number_f1(3,3,cnt);//��ʾ������ֵ
//					delay_cycles(DELAY);//��ʱ0.1��

    }
	
//    printf("Initialization Data Succeed \r\n");
	
//    while(1) 
//    {
//        //��ȡŷ���� Get Euler angles
//        if( mpu_dmp_get_data(&pitch,&roll,&yaw) == 0 )
//        { 
//            printf("\r\npitch =%d\r\n", (int)pitch);
//            printf("\r\nroll =%d\r\n", (int)roll);
//            printf("\r\nyaw =%d\r\n", (int)yaw);
//        }      
//        delay_ms(200);//�������õĲ����ʣ�����������ʱ���� According to the set sampling rate, the delay cannot be set too large
//    }

}

void TIMER_0_INST_IRQHandler(void)
{

    switch( DL_TimerG_getPendingInterrupt(TIMER_0_INST) )
    {
        case DL_TIMER_IIDX_ZERO://�����0����ж� If it is 0 overflow interrupt
			cnt++;
			huidu_read();       // 读取灰度传感器数据
			Poistion_PID();     // 计算位置PID控制
			Motor_Differential_Control();  // 执行差速电机控制

            break;

        default://�����Ķ�ʱ���ж� Other timer interrupts
            break;
    }
}

void OLED_Proc(void)
{
	// 显示电机速度信息
	sprintf((char *)str,"L:%2d   R:%2d  ",Read_Speed_L,Read_Speed_R);
	display_6_8_string(1, 0, (char *)str);

	// 显示位置误差
	sprintf((char *)str,"Pos:%3d",Position2);
	display_6_8_string(1, 2, (char *)str);

	// 显示灰度传感器状态 (8个传感器的二进制状态)
	sprintf((char *)str,"Gray:%d%d%d%d%d%d%d%d",
		HD2adc_value[7], HDadc_value[1], HDadc_value[2], HDadc_value[3],
		HDadc_value[4], HDadc_value[5], HDadc_value[6], HDadc_value[7]);
	display_6_8_string(1, 4, (char *)str);

	// 显示当前位置值
	sprintf((char *)str,"Line:%.1f", Position);
	display_6_8_string(1, 6, (char *)str);
}


