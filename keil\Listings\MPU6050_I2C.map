Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    empty.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.main) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers to board.o(.text.board_init) for board_init
    empty.o(.text.main) refers to drv_oled.o(.text.oled_init) for oled_init
    empty.o(.text.main) refers to puts.o(.text) for puts
    empty.o(.text.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    empty.o(.text.main) refers to drv_oled.o(.text.display_6_8_string) for display_6_8_string
    empty.o(.text.main) refers to control.o(.bss.Kp) for Kp
    empty.o(.text.main) refers to control.o(.bss.Kd) for Kd
    empty.o(.text.main) refers to motor.o(.bss.Read_Speed_R) for Read_Speed_R
    empty.o(.text.main) refers to motor.o(.bss.Read_Speed_L) for Read_Speed_L
    empty.o(.text.main) refers to empty.o(.bss.str) for str
    empty.o(.text.main) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.text.main) refers to control.o(.bss.Position2) for Position2
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.text.OLED_Proc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.OLED_Proc) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.text.OLED_Proc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.OLED_Proc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.OLED_Proc) refers to noretval__2sprintf.o(.text) for __2sprintf
    empty.o(.text.OLED_Proc) refers to drv_oled.o(.text.display_6_8_string) for display_6_8_string
    empty.o(.text.OLED_Proc) refers to motor.o(.bss.Read_Speed_R) for Read_Speed_R
    empty.o(.text.OLED_Proc) refers to motor.o(.bss.Read_Speed_L) for Read_Speed_L
    empty.o(.text.OLED_Proc) refers to empty.o(.bss.str) for str
    empty.o(.text.OLED_Proc) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.text.OLED_Proc) refers to control.o(.bss.Position2) for Position2
    empty.o(.ARM.exidx.text.OLED_Proc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.OLED_Proc) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.ARM.exidx.text.OLED_Proc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.OLED_Proc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.OLED_Proc) refers to empty.o(.text.OLED_Proc) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.TIMG0_IRQHandler) refers to huiduo.o(.text.huidu_read) for huidu_read
    empty.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Poistion_PID) for Poistion_PID
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.cnt) for cnt
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to empty.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    empty.o(.bss.cnt) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.cnt) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.bss.cnt) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.cnt) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.str) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.str) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.bss.str) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.str) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.rodata.str1.1) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to ccd.o(.text.ADC0_IRQHandler) for ADC0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to board.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init) for SYSCFG_DL_AO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) for SYSCFG_DL_ADC1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init) refers to ti_msp_dl_config.o(.rodata.gAOClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_AO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) refers to ti_msp_dl_config.o(.rodata.gADC1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    board.o(.text.board_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    board.o(.text.board_init) refers to puts.o(.text) for puts
    board.o(.ARM.exidx.text.board_init) refers to board.o(.text.board_init) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_us) refers to board.o(.text.delay_us) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_ms) refers to board.o(.text.delay_ms) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_1us) refers to board.o(.text.delay_1us) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_1ms) refers to board.o(.text.delay_1ms) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.uart0_send_char) refers to board.o(.text.uart0_send_char) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.uart0_send_string) refers to board.o(.text.uart0_send_string) for [Anonymous Symbol]
    board.o(.ARM.exidx.text._sys_exit) refers to board.o(.text._sys_exit) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.fputc) refers to board.o(.text.fputc) for [Anonymous Symbol]
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_length) for recv0_length
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_buff) for recv0_buff
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_flag) for recv0_flag
    board.o(.ARM.exidx.text.UART0_IRQHandler) refers to board.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    bsp_mpu6050.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.IIC_Start) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Start) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers to bsp_mpu6050.o(.text.IIC_Start) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.IIC_Stop) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Stop) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers to bsp_mpu6050.o(.text.IIC_Stop) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.IIC_Send_Ack) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.I2C_WaitAck) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.I2C_WaitAck) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.Send_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.Send_Byte) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers to bsp_mpu6050.o(.text.Send_Byte) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.Read_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.Read_Byte) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers to bsp_mpu6050.o(.text.Read_Byte) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.Send_Byte) for Send_Byte
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.Send_Byte) for Send_Byte
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.Read_Byte) for Read_Byte
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers to bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers to bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers to bsp_mpu6050.o(.text.MPU_Set_LPF) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU_Set_Rate) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers to bsp_mpu6050.o(.text.MPU6050ReadGyro) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers to bsp_mpu6050.o(.text.MPU6050ReadAcc) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to dflti.o(.text) for __aeabi_i2d
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to daddsub.o(.text) for __aeabi_dadd
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to d2f.o(.text) for __aeabi_d2f
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers to bsp_mpu6050.o(.text.MPU6050_GetTemp) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050ReadID) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to noretval__2printf.o(.text) for __2printf
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to bsp_mpu6050.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers to bsp_mpu6050.o(.text.MPU6050ReadID) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.text.MPU6050_Init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.text.MPU6050_Init) refers to board.o(.text.delay_ms) for delay_ms
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050_Init) refers to noretval__2printf.o(.text) for __2printf
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_Init) for [Anonymous Symbol]
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    bsp_mpu6050.o(.rodata.str1.1) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_reg_dump) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_reg_dump) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_reg_dump) refers to noretval__2printf.o(.text) for __2printf
    inv_mpu.o(.text.mpu_reg_dump) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers to inv_mpu.o(.text.mpu_reg_dump) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers to inv_mpu.o(.text.mpu_read_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_init) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_init) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_init) refers to puts.o(.text) for puts
    inv_mpu.o(.text.mpu_init) refers to noretval__2printf.o(.text) for __2printf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.rodata.str1.1) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers to inv_mpu.o(.text.mpu_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_lpf) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers to inv_mpu.o(.text.mpu_set_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_set_sample_rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_set_sample_rate) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_set_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_configure_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_configure_fifo) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_configure_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_bypass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_bypass) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_set_bypass) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers to inv_mpu.o(.text.mpu_set_bypass) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_sensors) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_sensors) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_sensors) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_set_sensors) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers to inv_mpu.o(.text.mpu_set_sensors) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_lp_accel_mode) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_int_latched) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_int_latched) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_int_latched) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers to inv_mpu.o(.text.mpu_set_int_latched) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_gyro_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers to inv_mpu.o(.text.mpu_get_gyro_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mget_ms) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers to inv_mpu.o(.text.mget_ms) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_accel_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_accel_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_accel_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers to inv_mpu.o(.text.mpu_get_accel_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_temperature) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_temperature) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_temperature) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_get_temperature) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(.text.mpu_get_temperature) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_get_temperature) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_get_temperature) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_get_temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.mpu_get_temperature) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers to inv_mpu.o(.text.mpu_get_temperature) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_accel_bias) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_set_accel_bias) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_accel_bias) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers to inv_mpu.o(.text.mpu_set_accel_bias) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_reset_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_reset_fifo) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_reset_fifo) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_reset_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.text.mpu_get_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers to inv_mpu.o(.text.mpu_get_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers to inv_mpu.o(.text.mpu_get_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers to inv_mpu.o(.text.mpu_get_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers to inv_mpu.o(.text.mpu_set_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_gyro_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_gyro_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers to inv_mpu.o(.text.mpu_get_gyro_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_accel_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_accel_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers to inv_mpu.o(.text.mpu_get_accel_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_fifo_config) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_fifo_config) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers to inv_mpu.o(.text.mpu_get_fifo_config) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_power_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_power_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers to inv_mpu.o(.text.mpu_get_power_state) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_int_status) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_int_status) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_int_status) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers to inv_mpu.o(.text.mpu_get_int_status) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_fifo) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_fifo) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_fifo_stream) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_int_level) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_int_level) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers to inv_mpu.o(.text.mpu_set_int_level) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.get_st_biases) for get_st_biases
    inv_mpu.o(.text.mpu_run_self_test) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_run_self_test) refers to fflti.o(.text) for __aeabi_ui2f
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._feq) for __aeabi_fcmpeq
    inv_mpu.o(.text.mpu_run_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_run_self_test) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_set_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_set_dmp_state) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.get_st_biases) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.get_st_biases) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.get_st_biases) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.get_st_biases) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(.text.get_st_biases) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers to inv_mpu.o(.text.get_st_biases) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_write_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_write_mem) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_write_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers to inv_mpu.o(.text.mpu_write_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_read_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers to inv_mpu.o(.text.mpu_read_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_load_firmware) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_load_firmware) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_load_firmware) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(.text.mpu_load_firmware) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_get_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers to inv_mpu.o(.text.mpu_get_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.setup_compass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers to inv_mpu.o(.text.setup_compass) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_compass_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers to inv_mpu.o(.text.mpu_get_compass_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_get_compass_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers to inv_mpu.o(.text.mpu_get_compass_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_motion_interrupt) for [Anonymous Symbol]
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(.text.run_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.run_self_test) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.run_self_test) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(.text.run_self_test) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers to inv_mpu.o(.text.run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(.text.inv_orientation_matrix_to_scalar) for [Anonymous Symbol]
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.inv_row_2_scale) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers to inv_mpu.o(.text.inv_row_2_scale) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_dmp_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_init) for mpu_init
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_dmp_init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.data.gyro_orientation) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_dmp_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.text.mpu_dmp_get_data) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.text.mpu_dmp_get_data) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(.text.mpu_dmp_get_data) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_dmp_get_data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_dmp_get_data) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_dmp_get_data) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(.text.mpu_dmp_get_data) refers to asin.o(i.asin) for asin
    inv_mpu.o(.text.mpu_dmp_get_data) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(.text.mpu_dmp_get_data) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text.mpu_dmp_get_data) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(.text.mpu_dmp_get_data) refers to atan2.o(i.atan2) for atan2
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers to inv_mpu.o(.text.mpu_dmp_get_data) for [Anonymous Symbol]
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.reg) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.hw) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.test) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.test) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.test) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.test) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.test) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.test) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.data.st) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.data.st) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.data.st) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.data.st) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.data.st) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.data.st) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.reg) for reg
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.hw) for hw
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.test) for test
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.data.gyro_orientation) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.str1.1) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata.cst8) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.4) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.4) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fflti.o(.text) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.5) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.5) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.0) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.1) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.0) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.1) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) for [Anonymous Symbol]
    drv_oled.o(.text.bsp_analog_i2c_init) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_init) refers to drv_oled.o(.text.bsp_analog_i2c_init) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_stop) refers to drv_oled.o(.text.bsp_analog_i2c_stop) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.i2c_sda_out) refers to drv_oled.o(.text.i2c_sda_out) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.i2c_sda_in) refers to drv_oled.o(.text.i2c_sda_in) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_start) refers to drv_oled.o(.text.bsp_analog_i2c_start) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_wait_ack) refers to drv_oled.o(.text.bsp_analog_i2c_wait_ack) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_ack) refers to drv_oled.o(.text.bsp_analog_i2c_ack) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_nack) refers to drv_oled.o(.text.bsp_analog_i2c_nack) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_send_byte) refers to drv_oled.o(.text.bsp_analog_i2c_send_byte) for [Anonymous Symbol]
    drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_read_byte) refers to drv_oled.o(.text.bsp_analog_i2c_read_byte) for [Anonymous Symbol]
    drv_oled.o(.text.OLED_WrDat) refers to drv_oled.o(.text.bsp_analog_i2c_send_byte) for bsp_analog_i2c_send_byte
    drv_oled.o(.ARM.exidx.text.OLED_WrDat) refers to drv_oled.o(.text.OLED_WrDat) for [Anonymous Symbol]
    drv_oled.o(.text.OLED_WrCmd) refers to drv_oled.o(.text.bsp_analog_i2c_send_byte) for bsp_analog_i2c_send_byte
    drv_oled.o(.ARM.exidx.text.OLED_WrCmd) refers to drv_oled.o(.text.OLED_WrCmd) for [Anonymous Symbol]
    drv_oled.o(.text.OLED_Set_Pos) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to drv_oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    drv_oled.o(.text.OLED_Fill) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.text.OLED_Fill) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    drv_oled.o(.ARM.exidx.text.OLED_Fill) refers to drv_oled.o(.text.OLED_Fill) for [Anonymous Symbol]
    drv_oled.o(.text.OLED_CLS) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.text.OLED_CLS) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    drv_oled.o(.ARM.exidx.text.OLED_CLS) refers to drv_oled.o(.text.OLED_CLS) for [Anonymous Symbol]
    drv_oled.o(.text.LCD_P6x8Str) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.text.LCD_P6x8Str) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    drv_oled.o(.text.LCD_P6x8Str) refers to drv_oled.o(.rodata.F6x8) for F6x8
    drv_oled.o(.ARM.exidx.text.LCD_P6x8Str) refers to drv_oled.o(.text.LCD_P6x8Str) for [Anonymous Symbol]
    drv_oled.o(.text.LCD_P6x8Char) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.text.LCD_P6x8Char) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    drv_oled.o(.text.LCD_P6x8Char) refers to drv_oled.o(.rodata.F6x8) for F6x8
    drv_oled.o(.ARM.exidx.text.LCD_P6x8Char) refers to drv_oled.o(.text.LCD_P6x8Char) for [Anonymous Symbol]
    drv_oled.o(.text.write_6_8_number) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    drv_oled.o(.text.write_6_8_number) refers to drv_oled.o(.text.LCD_P6x8Char) for LCD_P6x8Char
    drv_oled.o(.text.write_6_8_number) refers to ffixi.o(.text) for __aeabi_f2iz
    drv_oled.o(.text.write_6_8_number) refers to fflti.o(.text) for __aeabi_i2f
    drv_oled.o(.text.write_6_8_number) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    drv_oled.o(.text.write_6_8_number) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    drv_oled.o(.text.write_6_8_number) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    drv_oled.o(.text.write_6_8_number) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    drv_oled.o(.text.write_6_8_number) refers to drv_oled.o(.text.LCD_P6x8Str) for LCD_P6x8Str
    drv_oled.o(.ARM.exidx.text.write_6_8_number) refers to drv_oled.o(.text.write_6_8_number) for [Anonymous Symbol]
    drv_oled.o(.text.LCD_clear_L) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.text.LCD_clear_L) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    drv_oled.o(.ARM.exidx.text.LCD_clear_L) refers to drv_oled.o(.text.LCD_clear_L) for [Anonymous Symbol]
    drv_oled.o(.text.Draw_Logo) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    drv_oled.o(.text.Draw_Logo) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    drv_oled.o(.text.Draw_Logo) refers to drv_oled.o(.rodata.NC_Logo) for NC_Logo
    drv_oled.o(.ARM.exidx.text.Draw_Logo) refers to drv_oled.o(.text.Draw_Logo) for [Anonymous Symbol]
    drv_oled.o(.text.oled_init) refers to drv_oled.o(.text.bsp_analog_i2c_init) for bsp_analog_i2c_init
    drv_oled.o(.text.oled_init) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    drv_oled.o(.text.oled_init) refers to ssd1306.o(.text.ssd1306_begin) for ssd1306_begin
    drv_oled.o(.text.oled_init) refers to drv_oled.o(.text.Draw_Logo) for Draw_Logo
    drv_oled.o(.text.oled_init) refers to drv_oled.o(.text.OLED_Fill) for OLED_Fill
    drv_oled.o(.ARM.exidx.text.oled_init) refers to drv_oled.o(.text.oled_init) for [Anonymous Symbol]
    drv_oled.o(.text.display_6_8_number) refers to drv_oled.o(.text.write_6_8_number) for write_6_8_number
    drv_oled.o(.ARM.exidx.text.display_6_8_number) refers to drv_oled.o(.text.display_6_8_number) for [Anonymous Symbol]
    drv_oled.o(.text.display_6_8_string) refers to drv_oled.o(.text.LCD_P6x8Str) for LCD_P6x8Str
    drv_oled.o(.ARM.exidx.text.display_6_8_string) refers to drv_oled.o(.text.display_6_8_string) for [Anonymous Symbol]
    drv_oled.o(.text.display_6_8_number_pro) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    drv_oled.o(.text.display_6_8_number_pro) refers to drv_oled.o(.text.LCD_P6x8Char) for LCD_P6x8Char
    drv_oled.o(.text.display_6_8_number_pro) refers to drv_oled.o(.text.write_6_8_number) for write_6_8_number
    drv_oled.o(.ARM.exidx.text.display_6_8_number_pro) refers to drv_oled.o(.text.display_6_8_number_pro) for [Anonymous Symbol]
    drv_oled.o(.text.write_6_8_number_f1) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    drv_oled.o(.text.write_6_8_number_f1) refers to drv_oled.o(.text.LCD_P6x8Char) for LCD_P6x8Char
    drv_oled.o(.text.write_6_8_number_f1) refers to ffixi.o(.text) for __aeabi_f2iz
    drv_oled.o(.text.write_6_8_number_f1) refers to fflti.o(.text) for __aeabi_i2f
    drv_oled.o(.text.write_6_8_number_f1) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    drv_oled.o(.text.write_6_8_number_f1) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    drv_oled.o(.text.write_6_8_number_f1) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    drv_oled.o(.text.write_6_8_number_f1) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    drv_oled.o(.text.write_6_8_number_f1) refers to drv_oled.o(.text.LCD_P6x8Str) for LCD_P6x8Str
    drv_oled.o(.ARM.exidx.text.write_6_8_number_f1) refers to drv_oled.o(.text.write_6_8_number_f1) for [Anonymous Symbol]
    drv_oled.o(.text.display_6_8_number_f1) refers to drv_oled.o(.text.write_6_8_number_f1) for write_6_8_number_f1
    drv_oled.o(.ARM.exidx.text.display_6_8_number_f1) refers to drv_oled.o(.text.display_6_8_number_f1) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_width) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_width) refers to ssd1306.o(.text.ssd1306_width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_height) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_height) refers to ssd1306.o(.text.ssd1306_height) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.rodata.cst8) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) for [Anonymous Symbol]
    ssd1306.o(.text.set_rotation) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.set_rotation) refers to ssd1306.o(.text.set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_command) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_command) refers to ssd1306.o(.text.ssd1306_command) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._vccstate) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._cp437) for _cp437
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_begin) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_begin) refers to ssd1306.o(.text.ssd1306_begin) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_pixel) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_pixel) refers to ssd1306.o(.text.ssd1306_draw_pixel) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_invert_display) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_invert_display) refers to ssd1306.o(.text.ssd1306_invert_display) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_right) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_right) refers to ssd1306.o(.text.ssd1306_start_scroll_right) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_left) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_left) refers to ssd1306.o(.text.ssd1306_start_scroll_left) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_diag_right) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_right) refers to ssd1306.o(.text.ssd1306_start_scroll_diag_right) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_start_scroll_diag_left) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_left) refers to ssd1306.o(.text.ssd1306_start_scroll_diag_left) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_stop_scroll) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.ARM.exidx.text.ssd1306_stop_scroll) refers to ssd1306.o(.text.ssd1306_stop_scroll) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_dim) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.text.ssd1306_dim) refers to ssd1306.o(.bss._vccstate) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_dim) refers to ssd1306.o(.text.ssd1306_dim) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_data) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    ssd1306.o(.ARM.exidx.text.ssd1306_data) refers to ssd1306.o(.text.ssd1306_data) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_display) refers to drv_oled.o(.text.OLED_WrCmd) for OLED_WrCmd
    ssd1306.o(.text.ssd1306_display) refers to ssd1306.o(.text.draw_oled) for draw_oled
    ssd1306.o(.ARM.exidx.text.ssd1306_display) refers to ssd1306.o(.text.ssd1306_display) for [Anonymous Symbol]
    ssd1306.o(.text.draw_oled) refers to drv_oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    ssd1306.o(.text.draw_oled) refers to drv_oled.o(.text.OLED_WrDat) for OLED_WrDat
    ssd1306.o(.text.draw_oled) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.draw_oled) refers to ssd1306.o(.text.draw_oled) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_clear_display) refers to rt_memclr.o(.text) for __aeabi_memclr4
    ssd1306.o(.text.ssd1306_clear_display) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_clear_display) refers to ssd1306.o(.text.ssd1306_clear_display) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.text.ssd1306_draw_fast_vline_internal) for ssd1306_draw_fast_vline_internal
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline_internal) refers to ssd1306.o(.text.ssd1306_draw_fast_vline_internal) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline_internal) refers to ssd1306.o(.text.ssd1306_draw_fast_hline_internal) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.text.ssd1306_draw_fast_vline_internal) for ssd1306_draw_fast_vline_internal
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.data.buffer) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_circle) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle) refers to ssd1306.o(.text.ssd1306_draw_circle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_circle_helper) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle_helper) refers to ssd1306.o(.text.ssd1306_draw_circle_helper) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_circle) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_fill_circle) refers to ssd1306.o(.text.ssd1306_fill_circle_helper) for ssd1306_fill_circle_helper
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle) refers to ssd1306.o(.text.ssd1306_fill_circle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_circle_helper) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle_helper) refers to ssd1306.o(.text.ssd1306_fill_circle_helper) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_line) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_line) refers to ssd1306.o(.text.ssd1306_draw_line) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for ssd1306_draw_fast_hline
    ssd1306.o(.text.ssd1306_draw_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_rect) refers to ssd1306.o(.text.ssd1306_draw_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_rect) refers to ssd1306.o(.text.ssd1306_fill_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_screen) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_fill_screen) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_screen) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_screen) refers to ssd1306.o(.text.ssd1306_fill_screen) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for ssd1306_draw_fast_hline
    ssd1306.o(.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_round_rect) refers to ssd1306.o(.text.ssd1306_draw_round_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_round_rect) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_round_rect) refers to ssd1306.o(.text.ssd1306_fill_round_rect) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_triangle) refers to ssd1306.o(.text.ssd1306_draw_line) for ssd1306_draw_line
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_triangle) refers to ssd1306.o(.text.ssd1306_draw_triangle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_fill_triangle) refers to ssd1306.o(.text.ssd1306_draw_fast_hline) for ssd1306_draw_fast_hline
    ssd1306.o(.text.ssd1306_fill_triangle) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    ssd1306.o(.ARM.exidx.text.ssd1306_fill_triangle) refers to ssd1306.o(.text.ssd1306_fill_triangle) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_bitmap) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap) refers to ssd1306.o(.text.ssd1306_draw_bitmap) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_bitmap_bg) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap_bg) refers to ssd1306.o(.text.ssd1306_draw_bitmap_bg) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_xbitmap) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_xbitmap) refers to ssd1306.o(.text.ssd1306_draw_xbitmap) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.text.ssd1306_draw_char) for ssd1306_draw_char
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.text.ssd1306_write) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_write) refers to ssd1306.o(.text.ssd1306_write) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.text.ssd1306_draw_pixel) for ssd1306_draw_pixel
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.text.ssd1306_draw_fast_vline) for ssd1306_draw_fast_vline
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.bss._cp437) for _cp437
    ssd1306.o(.text.ssd1306_draw_char) refers to ssd1306.o(.rodata.font) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_draw_char) refers to ssd1306.o(.text.ssd1306_draw_char) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_cursor) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_cursor) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_cursor) refers to ssd1306.o(.text.ssd1306_set_cursor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_get_cursor_x) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_x) refers to ssd1306.o(.text.ssd1306_get_cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_get_cursor_y) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_y) refers to ssd1306.o(.text.ssd1306_get_cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textsize) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textsize) refers to ssd1306.o(.text.ssd1306_set_textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor) refers to ssd1306.o(.text.ssd1306_set_textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor_bg) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textcolor_bg) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor_bg) refers to ssd1306.o(.text.ssd1306_set_textcolor_bg) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_textwrap) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.ARM.exidx.text.ssd1306_set_textwrap) refers to ssd1306.o(.text.ssd1306_set_textwrap) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_get_rotation) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_get_rotation) refers to ssd1306.o(.text.ssd1306_get_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.bss.rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.rodata.cst8) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_set_rotation) refers to ssd1306.o(.bss._height) for [Anonymous Symbol]
    ssd1306.o(.ARM.exidx.text.ssd1306_set_rotation) refers to ssd1306.o(.text.ssd1306_set_rotation) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_cp437) refers to ssd1306.o(.bss._cp437) for _cp437
    ssd1306.o(.ARM.exidx.text.ssd1306_cp437) refers to ssd1306.o(.text.ssd1306_cp437) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.text.ssd1306_draw_char) for ssd1306_draw_char
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_putstring) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.ARM.exidx.text.ssd1306_putstring) refers to ssd1306.o(.text.ssd1306_putstring) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.text.ssd1306_draw_char) for ssd1306_draw_char
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.textsize) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss._width) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.textbgcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.textcolor) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.cursor_x) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.cursor_y) for [Anonymous Symbol]
    ssd1306.o(.text.ssd1306_puts) refers to ssd1306.o(.bss.wrap) for wrap
    ssd1306.o(.ARM.exidx.text.ssd1306_puts) refers to ssd1306.o(.text.ssd1306_puts) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) refers to ssd1306.o(.bss.HEIGHT) for [Anonymous Symbol]
    ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6) refers to ssd1306.o(.bss.WIDTH) for [Anonymous Symbol]
    ccd.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.adc_getValue) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.adc_getValue) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.adc_getValue) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.adc_getValue) refers to ccd.o(.bss.gCheckADC) for gCheckADC
    ccd.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.adc_getValue) refers to ccd.o(.text.adc_getValue) for [Anonymous Symbol]
    ccd.o(.text.ADC0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.ADC0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.ADC0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.ADC0_IRQHandler) refers to ccd.o(.bss.gCheckADC) for gCheckADC
    ccd.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.ADC0_IRQHandler) refers to ccd.o(.text.ADC0_IRQHandler) for [Anonymous Symbol]
    ccd.o(.text.RD_TSL) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.RD_TSL) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.RD_TSL) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.RD_TSL) refers to board.o(.text.delay_us) for delay_us
    ccd.o(.text.RD_TSL) refers to ccd.o(.bss.gCheckADC) for gCheckADC
    ccd.o(.text.RD_TSL) refers to ccd.o(.bss.ADV) for ADV
    ccd.o(.ARM.exidx.text.RD_TSL) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.RD_TSL) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.RD_TSL) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.RD_TSL) refers to ccd.o(.text.RD_TSL) for [Anonymous Symbol]
    ccd.o(.text.sendToPc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.sendToPc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.sendToPc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.sendToPc) refers to ccd.o(.text.RD_TSL) for RD_TSL
    ccd.o(.text.sendToPc) refers to noretval__2printf.o(.text) for __2printf
    ccd.o(.text.sendToPc) refers to puts.o(.text) for puts
    ccd.o(.text.sendToPc) refers to ccd.o(.bss.ADV) for ADV
    ccd.o(.text.sendToPc) refers to ccd.o(.bss.SciBuf) for SciBuf
    ccd.o(.text.sendToPc) refers to ccd.o(.rodata.str1.1) for [Anonymous Symbol]
    ccd.o(.ARM.exidx.text.sendToPc) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.sendToPc) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.sendToPc) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.sendToPc) refers to ccd.o(.text.sendToPc) for [Anonymous Symbol]
    ccd.o(.text.slove_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.slove_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.slove_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.slove_data) refers to ccd.o(.text.RD_TSL) for RD_TSL
    ccd.o(.text.slove_data) refers to ccd.o(.bss.ADV) for ADV
    ccd.o(.text.slove_data) refers to ccd.o(.bss.SciBuf) for SciBuf
    ccd.o(.ARM.exidx.text.slove_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.slove_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.slove_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.slove_data) refers to ccd.o(.text.slove_data) for [Anonymous Symbol]
    ccd.o(.text.Find_CCD_Zhongzhi) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.Find_CCD_Zhongzhi) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.Find_CCD_Zhongzhi) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.Find_CCD_Zhongzhi) refers to ccd.o(.bss.ADV) for ADV
    ccd.o(.text.Find_CCD_Zhongzhi) refers to ccd.o(.bss.CCD_Yuzhi) for CCD_Yuzhi
    ccd.o(.text.Find_CCD_Zhongzhi) refers to ccd.o(.bss.Find_CCD_Zhongzhi.Left) for [Anonymous Symbol]
    ccd.o(.text.Find_CCD_Zhongzhi) refers to ccd.o(.bss.Find_CCD_Zhongzhi.Right) for [Anonymous Symbol]
    ccd.o(.text.Find_CCD_Zhongzhi) refers to ccd.o(.bss.buf_CCD) for buf_CCD
    ccd.o(.text.Find_CCD_Zhongzhi) refers to ccd.o(.bss.CCD_Zhongzhi) for CCD_Zhongzhi
    ccd.o(.ARM.exidx.text.Find_CCD_Zhongzhi) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.Find_CCD_Zhongzhi) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.Find_CCD_Zhongzhi) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.Find_CCD_Zhongzhi) refers to ccd.o(.text.Find_CCD_Zhongzhi) for [Anonymous Symbol]
    ccd.o(.text.deal_data_ccd) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.text.deal_data_ccd) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.text.deal_data_ccd) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.text.deal_data_ccd) refers to ccd.o(.text.RD_TSL) for RD_TSL
    ccd.o(.text.deal_data_ccd) refers to ccd.o(.text.Find_CCD_Zhongzhi) for Find_CCD_Zhongzhi
    ccd.o(.ARM.exidx.text.deal_data_ccd) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.ARM.exidx.text.deal_data_ccd) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.ARM.exidx.text.deal_data_ccd) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.ARM.exidx.text.deal_data_ccd) refers to ccd.o(.text.deal_data_ccd) for [Anonymous Symbol]
    ccd.o(.bss.buf_CCD) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.buf_CCD) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.buf_CCD) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.ADV) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.ADV) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.ADV) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.gCheckADC) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.gCheckADC) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.gCheckADC) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.SciBuf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.SciBuf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.SciBuf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.Find_CCD_Zhongzhi.Left) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.Find_CCD_Zhongzhi.Left) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.Find_CCD_Zhongzhi.Left) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.Find_CCD_Zhongzhi.Right) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.Find_CCD_Zhongzhi.Right) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.Find_CCD_Zhongzhi.Right) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.CCD_Yuzhi) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.CCD_Yuzhi) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.CCD_Yuzhi) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    ccd.o(.bss.CCD_Zhongzhi) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ccd.o(.bss.CCD_Zhongzhi) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ccd.o(.bss.CCD_Zhongzhi) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    huiduo.o(.text.huidu_read) refers to huiduo.o(.bss.lsqgCheckADC) for lsqgCheckADC
    huiduo.o(.text.huidu_read) refers to huiduo.o(.bss.HDadc_value) for HDadc_value
    huiduo.o(.text.huidu_read) refers to huiduo.o(.bss.HD2adc_value) for HD2adc_value
    huiduo.o(.text.huidu_read) refers to huiduo.o(.bss.i) for i
    huiduo.o(.ARM.exidx.text.huidu_read) refers to huiduo.o(.text.huidu_read) for [Anonymous Symbol]
    huiduo.o(.text.lsqadc_getValue) refers to huiduo.o(.bss.lsqgCheckADC) for lsqgCheckADC
    huiduo.o(.ARM.exidx.text.lsqadc_getValue) refers to huiduo.o(.text.lsqadc_getValue) for [Anonymous Symbol]
    huiduo.o(.text.ADC_VOLTAGE_INST_IRQHandler) refers to huiduo.o(.bss.lsqgCheckADC) for lsqgCheckADC
    huiduo.o(.ARM.exidx.text.ADC_VOLTAGE_INST_IRQHandler) refers to huiduo.o(.text.ADC_VOLTAGE_INST_IRQHandler) for [Anonymous Symbol]
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.posetion) for posetion
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.HD2adc_value) for HD2adc_value
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.HDadc_value) for HDadc_value
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.er0) for er0
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.er1) for er1
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.sover_ksd) for sover_ksd
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.sover_ksp) for sover_ksp
    huiduo.o(.text.PD_Motor_huidu) refers to huiduo.o(.bss.duty) for duty
    huiduo.o(.ARM.exidx.text.PD_Motor_huidu) refers to huiduo.o(.text.PD_Motor_huidu) for [Anonymous Symbol]
    control.o(.text.Poistion_Error) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.Poistion_Error) refers to huiduo.o(.bss.HD2adc_value) for HD2adc_value
    control.o(.text.Poistion_Error) refers to huiduo.o(.bss.HDadc_value) for HDadc_value
    control.o(.text.Poistion_Error) refers to control.o(.bss.Position) for Position
    control.o(.ARM.exidx.text.Poistion_Error) refers to control.o(.text.Poistion_Error) for [Anonymous Symbol]
    control.o(.text.Poistion_PID) refers to control.o(.text.Poistion_Error) for Poistion_Error
    control.o(.text.Poistion_PID) refers to fflti.o(.text) for __aeabi_i2f
    control.o(.text.Poistion_PID) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    control.o(.text.Poistion_PID) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    control.o(.text.Poistion_PID) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.Poistion_PID) refers to control.o(.bss.Pos_error_now) for Pos_error_now
    control.o(.text.Poistion_PID) refers to control.o(.bss.Pos_error_last) for Pos_error_last
    control.o(.text.Poistion_PID) refers to control.o(.bss.Kd) for Kd
    control.o(.text.Poistion_PID) refers to control.o(.bss.Kp) for Kp
    control.o(.text.Poistion_PID) refers to control.o(.bss.Position2) for Position2
    control.o(.text.Poistion_PID) refers to control.o(.bss.Position2_Last) for Position2_Last
    control.o(.ARM.exidx.text.Poistion_PID) refers to control.o(.text.Poistion_PID) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.Set_PWM) refers to motor.o(.text.Set_PWM) for [Anonymous Symbol]
    motor.o(.text.Motor_PI) refers to fflti.o(.text) for __aeabi_i2f
    motor.o(.text.Motor_PI) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    motor.o(.text.Motor_PI) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    motor.o(.text.Motor_PI) refers to ffixi.o(.text) for __aeabi_f2iz
    motor.o(.text.Motor_PI) refers to motor.o(.bss.Read_Speed_L) for Read_Speed_L
    motor.o(.text.Motor_PI) refers to motor.o(.bss.Set_Speed_L) for Set_Speed_L
    motor.o(.text.Motor_PI) refers to motor.o(.bss.ek1) for ek1
    motor.o(.text.Motor_PI) refers to motor.o(.bss.ek2) for ek2
    motor.o(.text.Motor_PI) refers to motor.o(.bss.Read_Speed_R) for Read_Speed_R
    motor.o(.text.Motor_PI) refers to motor.o(.bss.Set_Speed_R) for Set_Speed_R
    motor.o(.text.Motor_PI) refers to motor.o(.bss.ek3) for ek3
    motor.o(.text.Motor_PI) refers to motor.o(.bss.ek4) for ek4
    motor.o(.text.Motor_PI) refers to motor.o(.bss.IL) for IL
    motor.o(.text.Motor_PI) refers to motor.o(.bss.PL) for PL
    motor.o(.text.Motor_PI) refers to motor.o(.bss.OutL) for OutL
    motor.o(.text.Motor_PI) refers to motor.o(.bss.IR) for IR
    motor.o(.text.Motor_PI) refers to motor.o(.bss.PR) for PR
    motor.o(.text.Motor_PI) refers to motor.o(.bss.OutR) for OutR
    motor.o(.ARM.exidx.text.Motor_PI) refers to motor.o(.text.Motor_PI) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for DL_FlashCTL_massErase
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for DL_FlashCTL_massEraseFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    llsdiv.o(.text) refers to lludiv.o(.text) for __aeabi_uldivmod
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to board.o(.bss.__stdout) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to board.o(.bss.__stdout) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    puts.o(.text) refers to board.o(.text.fputc) for fputc
    puts.o(.text) refers to board.o(.bss.__stdout) for __stdout
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i.__eqsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__eqsf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i.__gesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gesf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__gtsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gtsf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__lesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__lesf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__ltsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__ltsf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__nesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__nesf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to daddsub.o(.text) for __aeabi_dadd
    asin.o(i.asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.asin) refers to dscalbn.o(.text) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to board.o(.text.fputc) for fputc
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalbn.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalbn.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    dsqrt.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to board.o(.text._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to board.o(.text._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to board.o(.text._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.text.OLED_Proc), (84 bytes).
    Removing empty.o(.ARM.exidx.text.OLED_Proc), (8 bytes).
    Removing empty.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_AO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing board.o(.text), (0 bytes).
    Removing board.o(.ARM.exidx.text.board_init), (8 bytes).
    Removing board.o(.text.delay_us), (68 bytes).
    Removing board.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing board.o(.text.delay_ms), (72 bytes).
    Removing board.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing board.o(.text.delay_1us), (68 bytes).
    Removing board.o(.ARM.exidx.text.delay_1us), (8 bytes).
    Removing board.o(.text.delay_1ms), (72 bytes).
    Removing board.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing board.o(.text.uart0_send_char), (36 bytes).
    Removing board.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing board.o(.text.uart0_send_string), (56 bytes).
    Removing board.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing board.o(.ARM.exidx.text._sys_exit), (8 bytes).
    Removing board.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing board.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing bsp_mpu6050.o(.text), (0 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Start), (56 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Stop), (52 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Send_Ack), (76 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack), (8 bytes).
    Removing bsp_mpu6050.o(.text.I2C_WaitAck), (236 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck), (8 bytes).
    Removing bsp_mpu6050.o(.text.Send_Byte), (332 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.Send_Byte), (8 bytes).
    Removing bsp_mpu6050.o(.text.Read_Byte), (332 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.Read_Byte), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_WriteReg), (840 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_ReadData), (896 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr), (16 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr), (16 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_LPF), (64 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Rate), (104 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadGyro), (50 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadAcc), (50 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_GetTemp), (68 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadID), (76 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_Init), (304 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init), (8 bytes).
    Removing bsp_mpu6050.o(.rodata.str1.1), (51 bytes).
    Removing inv_mpu.o(.text), (0 bytes).
    Removing inv_mpu.o(.text.mpu_reg_dump), (112 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reg_dump), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_reg), (48 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_init), (408 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_init), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_gyro_fsr), (136 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_accel_fsr), (108 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_lpf), (116 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_lpf), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_sample_rate), (216 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_configure_fifo), (168 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_bypass), (240 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_bypass), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_sensors), (212 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sensors), (8 bytes).
    Removing inv_mpu.o(.text.mpu_lp_accel_mode), (488 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_latched), (108 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_reg), (72 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg), (8 bytes).
    Removing inv_mpu.o(.text.mget_ms), (2 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mget_ms), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_reg), (68 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_temperature), (120 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_temperature), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_accel_bias), (220 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias), (8 bytes).
    Removing inv_mpu.o(.text.mpu_reset_fifo), (332 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_fsr), (32 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_fsr), (52 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_lpf), (36 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_lpf), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_sample_rate), (28 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_compass_sample_rate), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_sens), (72 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_sens), (60 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_power_state), (20 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_power_state), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_int_status), (56 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_int_status), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_fifo), (352 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_fifo_stream), (164 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_level), (8 bytes).
    Removing inv_mpu.o(.text.mpu_run_self_test), (1944 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_run_self_test), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_dmp_state), (208 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.get_st_biases), (800 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.get_st_biases), (8 bytes).
    Removing inv_mpu.o(.text.mpu_write_mem), (108 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_write_mem), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_mem), (108 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_mem), (8 bytes).
    Removing inv_mpu.o(.text.mpu_load_firmware), (300 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_load_firmware), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_dmp_state), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.setup_compass), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.setup_compass), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_reg), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_fsr), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_lp_motion_interrupt), (816 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt), (8 bytes).
    Removing inv_mpu.o(.text.run_self_test), (228 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.run_self_test), (8 bytes).
    Removing inv_mpu.o(.text.inv_orientation_matrix_to_scalar), (188 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar), (8 bytes).
    Removing inv_mpu.o(.text.inv_row_2_scale), (56 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.inv_row_2_scale), (8 bytes).
    Removing inv_mpu.o(.text.mpu_dmp_init), (236 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_dmp_init), (8 bytes).
    Removing inv_mpu.o(.text.mpu_dmp_get_data), (456 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data), (8 bytes).
    Removing inv_mpu.o(.rodata.reg), (27 bytes).
    Removing inv_mpu.o(.rodata.hw), (12 bytes).
    Removing inv_mpu.o(.rodata.test), (40 bytes).
    Removing inv_mpu.o(.data.st), (44 bytes).
    Removing inv_mpu.o(.data.gyro_orientation), (9 bytes).
    Removing inv_mpu.o(.rodata.str1.1), (76 bytes).
    Removing inv_mpu.o(.rodata.cst8), (24 bytes).
    Removing inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.11), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text), (0 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware), (28 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation), (216 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias), (236 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias), (200 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate), (108 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh), (308 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh), (46 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count), (34 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time), (40 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature), (684 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal), (68 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode), (84 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo), (420 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory), (3062 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes), (3 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes), (3 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp.0), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp.1), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp.2), (2 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp.3), (2 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp.4), (2 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp.5), (1 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_fifo_rate.regs_end), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__unnamed_1), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__unnamed_2), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata.str1.4), (24 bytes).
    Removing drv_oled.o(.text), (0 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_init), (8 bytes).
    Removing drv_oled.o(.text.bsp_analog_i2c_stop), (168 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_stop), (8 bytes).
    Removing drv_oled.o(.text.i2c_sda_out), (24 bytes).
    Removing drv_oled.o(.ARM.exidx.text.i2c_sda_out), (8 bytes).
    Removing drv_oled.o(.text.i2c_sda_in), (16 bytes).
    Removing drv_oled.o(.ARM.exidx.text.i2c_sda_in), (8 bytes).
    Removing drv_oled.o(.text.bsp_analog_i2c_start), (236 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_start), (8 bytes).
    Removing drv_oled.o(.text.bsp_analog_i2c_wait_ack), (244 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_wait_ack), (8 bytes).
    Removing drv_oled.o(.text.bsp_analog_i2c_ack), (304 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_ack), (8 bytes).
    Removing drv_oled.o(.text.bsp_analog_i2c_nack), (236 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_nack), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_send_byte), (8 bytes).
    Removing drv_oled.o(.text.bsp_analog_i2c_read_byte), (320 bytes).
    Removing drv_oled.o(.ARM.exidx.text.bsp_analog_i2c_read_byte), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.OLED_WrDat), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.OLED_WrCmd), (8 bytes).
    Removing drv_oled.o(.text.OLED_Set_Pos), (32 bytes).
    Removing drv_oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.OLED_Fill), (8 bytes).
    Removing drv_oled.o(.text.OLED_CLS), (276 bytes).
    Removing drv_oled.o(.ARM.exidx.text.OLED_CLS), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.LCD_P6x8Str), (8 bytes).
    Removing drv_oled.o(.text.LCD_P6x8Char), (100 bytes).
    Removing drv_oled.o(.ARM.exidx.text.LCD_P6x8Char), (8 bytes).
    Removing drv_oled.o(.text.write_6_8_number), (720 bytes).
    Removing drv_oled.o(.ARM.exidx.text.write_6_8_number), (8 bytes).
    Removing drv_oled.o(.text.LCD_clear_L), (72 bytes).
    Removing drv_oled.o(.ARM.exidx.text.LCD_clear_L), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.Draw_Logo), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.oled_init), (8 bytes).
    Removing drv_oled.o(.text.display_6_8_number), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.display_6_8_number), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.display_6_8_string), (8 bytes).
    Removing drv_oled.o(.text.display_6_8_number_pro), (54 bytes).
    Removing drv_oled.o(.ARM.exidx.text.display_6_8_number_pro), (8 bytes).
    Removing drv_oled.o(.text.write_6_8_number_f1), (648 bytes).
    Removing drv_oled.o(.ARM.exidx.text.write_6_8_number_f1), (8 bytes).
    Removing drv_oled.o(.text.display_6_8_number_f1), (8 bytes).
    Removing drv_oled.o(.ARM.exidx.text.display_6_8_number_f1), (8 bytes).
    Removing ssd1306.o(.text), (0 bytes).
    Removing ssd1306.o(.text.ssd1306_width), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_width), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_height), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_height), (8 bytes).
    Removing ssd1306.o(.text.set_rotation), (80 bytes).
    Removing ssd1306.o(.ARM.exidx.text.set_rotation), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_command), (8 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_command), (8 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_begin), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_pixel), (240 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_pixel), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_invert_display), (22 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_invert_display), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_right), (58 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_right), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_left), (58 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_left), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_diag_right), (70 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_right), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_start_scroll_diag_left), (70 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_start_scroll_diag_left), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_stop_scroll), (10 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_stop_scroll), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_dim), (44 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_dim), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_data), (8 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_data), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_display), (46 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_display), (8 bytes).
    Removing ssd1306.o(.text.draw_oled), (244 bytes).
    Removing ssd1306.o(.ARM.exidx.text.draw_oled), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_clear_display), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_clear_display), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_hline), (492 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_vline_internal), (496 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline_internal), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_hline_internal), (396 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_hline_internal), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_fast_vline), (492 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_fast_vline), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_circle), (280 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_circle_helper), (314 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_circle_helper), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_circle), (50 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_circle_helper), (230 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_circle_helper), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_line), (184 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_line), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_rect), (70 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_rect), (46 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_screen), (52 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_screen), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_round_rect), (570 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_round_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_round_rect), (342 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_round_rect), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_triangle), (56 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_triangle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_fill_triangle), (418 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_fill_triangle), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_bitmap), (120 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_bitmap_bg), (130 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_bitmap_bg), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_xbitmap), (120 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_xbitmap), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_write), (148 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_write), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_draw_char), (304 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_draw_char), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_cursor), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_cursor), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_get_cursor_x), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_x), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_get_cursor_y), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_get_cursor_y), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textsize), (16 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textsize), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textcolor), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textcolor_bg), (20 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textcolor_bg), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_textwrap), (16 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_textwrap), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_get_rotation), (12 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_get_rotation), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_set_rotation), (80 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_set_rotation), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_cp437), (16 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_cp437), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_putstring), (160 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_putstring), (8 bytes).
    Removing ssd1306.o(.text.ssd1306_puts), (180 bytes).
    Removing ssd1306.o(.ARM.exidx.text.ssd1306_puts), (8 bytes).
    Removing ssd1306.o(.rodata.font), (1275 bytes).
    Removing ssd1306.o(.data.buffer), (1024 bytes).
    Removing ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation), (16 bytes).
    Removing ssd1306.o(.rodata.cst8), (16 bytes).
    Removing ssd1306.o(.rodata..Lswitch.table.ssd1306_set_rotation.6), (16 bytes).
    Removing glcdfont.o(.text), (0 bytes).
    Removing ccd.o(.text), (0 bytes).
    Removing ccd.o(.text.adc_getValue), (72 bytes).
    Removing ccd.o(.ARM.exidx.text.adc_getValue), (8 bytes).
    Removing ccd.o(.ARM.exidx.text.ADC0_IRQHandler), (8 bytes).
    Removing ccd.o(.text.RD_TSL), (172 bytes).
    Removing ccd.o(.ARM.exidx.text.RD_TSL), (8 bytes).
    Removing ccd.o(.text.sendToPc), (164 bytes).
    Removing ccd.o(.ARM.exidx.text.sendToPc), (8 bytes).
    Removing ccd.o(.text.slove_data), (96 bytes).
    Removing ccd.o(.ARM.exidx.text.slove_data), (8 bytes).
    Removing ccd.o(.text.Find_CCD_Zhongzhi), (444 bytes).
    Removing ccd.o(.ARM.exidx.text.Find_CCD_Zhongzhi), (8 bytes).
    Removing ccd.o(.text.deal_data_ccd), (12 bytes).
    Removing ccd.o(.ARM.exidx.text.deal_data_ccd), (8 bytes).
    Removing ccd.o(.bss.buf_CCD), (20 bytes).
    Removing ccd.o(.bss.ADV), (256 bytes).
    Removing ccd.o(.rodata.str1.1), (79 bytes).
    Removing ccd.o(.bss.SciBuf), (200 bytes).
    Removing ccd.o(.bss.Find_CCD_Zhongzhi.Left), (2 bytes).
    Removing ccd.o(.bss.Find_CCD_Zhongzhi.Right), (2 bytes).
    Removing ccd.o(.bss.CCD_Yuzhi), (1 bytes).
    Removing ccd.o(.bss.CCD_Zhongzhi), (1 bytes).
    Removing huiduo.o(.text), (0 bytes).
    Removing huiduo.o(.ARM.exidx.text.huidu_read), (8 bytes).
    Removing huiduo.o(.text.lsqadc_getValue), (40 bytes).
    Removing huiduo.o(.ARM.exidx.text.lsqadc_getValue), (8 bytes).
    Removing huiduo.o(.text.ADC_VOLTAGE_INST_IRQHandler), (28 bytes).
    Removing huiduo.o(.ARM.exidx.text.ADC_VOLTAGE_INST_IRQHandler), (8 bytes).
    Removing huiduo.o(.text.PD_Motor_huidu), (164 bytes).
    Removing huiduo.o(.ARM.exidx.text.PD_Motor_huidu), (8 bytes).
    Removing huiduo.o(.bss.er0), (2 bytes).
    Removing huiduo.o(.bss.er1), (2 bytes).
    Removing huiduo.o(.bss.sover_ksp), (2 bytes).
    Removing huiduo.o(.bss.sover_ksd), (2 bytes).
    Removing huiduo.o(.bss.posetion), (4 bytes).
    Removing huiduo.o(.bss.duty), (4 bytes).
    Removing huiduo.o(.bss.delay_times), (4 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.bss.ADC_Value), (10 bytes).
    Removing control.o(.text), (0 bytes).
    Removing control.o(.ARM.exidx.text.Poistion_Error), (8 bytes).
    Removing control.o(.ARM.exidx.text.Poistion_PID), (8 bytes).
    Removing control.o(.bss.sensor), (5 bytes).
    Removing control.o(.bss.Distance), (8 bytes).
    Removing control.o(.bss.Time_Count), (2 bytes).
    Removing control.o(.bss.Turn_Left_Flag), (1 bytes).
    Removing control.o(.bss.Turn_Right_Flag), (1 bytes).
    Removing control.o(.bss.Turn180_Flag), (1 bytes).
    Removing control.o(.bss.AD_L_R_Diff), (2 bytes).
    Removing control.o(.bss.Kd_tly), (4 bytes).
    Removing control.o(.bss.Target_S), (4 bytes).
    Removing control.o(.bss.Kp_S), (4 bytes).
    Removing control.o(.bss.Kd_S), (4 bytes).
    Removing control.o(.bss.Stop_Flag), (1 bytes).
    Removing control.o(.bss.State), (1 bytes).
    Removing control.o(.bss.Turn_dis_Flag), (1 bytes).
    Removing control.o(.bss.Find_Distance), (2 bytes).
    Removing control.o(.bss.Distance_01), (2 bytes).
    Removing control.o(.bss.Stop_Count), (1 bytes).
    Removing control.o(.bss.Pass_Flag), (1 bytes).
    Removing control.o(.bss.Zero_Flag), (1 bytes).
    Removing control.o(.bss.Stop_All_Flag), (1 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.text.Set_PWM), (2 bytes).
    Removing motor.o(.ARM.exidx.text.Set_PWM), (8 bytes).
    Removing motor.o(.text.Motor_PI), (276 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_PI), (8 bytes).
    Removing motor.o(.bss.Set_Speed_L), (4 bytes).
    Removing motor.o(.bss.ek1), (4 bytes).
    Removing motor.o(.bss.PL), (4 bytes).
    Removing motor.o(.bss.ek2), (4 bytes).
    Removing motor.o(.bss.IL), (4 bytes).
    Removing motor.o(.bss.OutL), (4 bytes).
    Removing motor.o(.bss.Set_Speed_R), (4 bytes).
    Removing motor.o(.bss.ek3), (4 bytes).
    Removing motor.o(.bss.PR), (4 bytes).
    Removing motor.o(.bss.ek4), (4 bytes).
    Removing motor.o(.bss.IR), (4 bytes).
    Removing motor.o(.bss.OutR), (4 bytes).
    Removing motor.o(.bss.Set_Speed), (4 bytes).
    Removing motor.o(.bss.S), (4 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (52 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (84 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (80 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (92 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (52 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (156 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (104 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (140 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (160 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (36 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.text.DL_DMA_initChannel), (68 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (116 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (288 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (200 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (224 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (156 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (464 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (100 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (12 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (100 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (188 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (204 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (152 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (220 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (176 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (212 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (136 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (252 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (128 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (132 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (40 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (236 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (224 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (560 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (228 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (252 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (56 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (72 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (220 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (128 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (46 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (304 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (344 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (52 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (48 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (160 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (132 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (100 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (88 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (76 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (100 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (88 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (76 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.text.DL_SPI_init), (68 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_setClockConfig), (18 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (48 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (48 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (124 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (124 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (124 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (268 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (116 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (224 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (160 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (108 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (308 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (304 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initFourCCPWMMode), (292 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (344 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (356 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (48 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (68 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (68 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (48 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (36 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (144 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (164 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (32 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (18 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (16 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).

1076 unused section(s) (total 59205 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  puts.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/dsqrt.c                  0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  dscalbn.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ADC.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    Control.c                                0x00000000   Number         0  control.o ABSOLUTE
    Motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    board.c                                  0x00000000   Number         0  board.o ABSOLUTE
    bsp_mpu6050.c                            0x00000000   Number         0  bsp_mpu6050.o ABSOLUTE
    ccd.c                                    0x00000000   Number         0  ccd.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    drv_oled.c                               0x00000000   Number         0  drv_oled.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    glcdfont.c                               0x00000000   Number         0  glcdfont.o ABSOLUTE
    huiduo.c                                 0x00000000   Number         0  huiduo.o ABSOLUTE
    inv_mpu.c                                0x00000000   Number         0  inv_mpu.o ABSOLUTE
    inv_mpu_dmp_motion_driver.c              0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ssd1306.c                                0x00000000   Number         0  ssd1306.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_null                           0x00000120   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000128   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000144   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x00000146   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x00000150   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x00000154   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000156   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000156   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000158   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000015a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000015a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000015a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000015a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000015a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000015a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000015a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000015c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000015c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000015c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000162   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000162   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000166   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000166   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000016e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000170   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000170   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000174   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000017c   Section       44  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001a8   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x000001d0   Section        0  _printf_pad.o(.text)
    .text                                    0x00000220   Section        0  _printf_dec.o(.text)
    .text                                    0x0000028c   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x000003c4   Section        0  puts.o(.text)
    .text                                    0x000003f0   Section        0  heapauxi.o(.text)
    .text                                    0x000003f6   Section        0  ffixi.o(.text)
    .text                                    0x00000442   Section        0  fflti.o(.text)
    .text                                    0x000004a0   Section        0  _printf_intcommon.o(.text)
    _printf_input_char                       0x00000551   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x00000550   Section        0  _printf_char_common.o(.text)
    .text                                    0x00000580   Section        0  _sputc.o(.text)
    .text                                    0x0000058a   Section        0  rtudiv10.o(.text)
    .text                                    0x000005b4   Section        8  libspace.o(.text)
    .text                                    0x000005bc   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x000005fa   Section        0  exit.o(.text)
    [Anonymous Symbol]                       0x0000060c   Section        0  ccd.o(.text.ADC0_IRQHandler)
    __arm_cp.1_0                             0x00000620   Number         4  ccd.o(.text.ADC0_IRQHandler)
    __arm_cp.1_1                             0x00000624   Number         4  ccd.o(.text.ADC0_IRQHandler)
    [Anonymous Symbol]                       0x00000628   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00000660   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x00000664   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    [Anonymous Symbol]                       0x00000668   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00000674   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00000754   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x00000758   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x0000075c   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00000760   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00000764   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x0000077c   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00000780   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x000007c0   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x000007c4   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x000007c8   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x000007dc   Section        0  drv_oled.o(.text.Draw_Logo)
    __arm_cp.19_0                            0x00000914   Number         4  drv_oled.o(.text.Draw_Logo)
    __arm_cp.19_1                            0x00000918   Number         4  drv_oled.o(.text.Draw_Logo)
    __arm_cp.19_2                            0x0000091c   Number         4  drv_oled.o(.text.Draw_Logo)
    [Anonymous Symbol]                       0x00000920   Section        0  drv_oled.o(.text.LCD_P6x8Str)
    __arm_cp.15_0                            0x000009a8   Number         4  drv_oled.o(.text.LCD_P6x8Str)
    [Anonymous Symbol]                       0x000009ac   Section        0  drv_oled.o(.text.OLED_Fill)
    [Anonymous Symbol]                       0x00000ac4   Section        0  drv_oled.o(.text.OLED_WrCmd)
    [Anonymous Symbol]                       0x00000c54   Section        0  drv_oled.o(.text.OLED_WrDat)
    __arm_cp.10_0                            0x00000de4   Number         4  drv_oled.o(.text.OLED_WrDat)
    __arm_cp.10_1                            0x00000de8   Number         4  drv_oled.o(.text.OLED_WrDat)
    [Anonymous Symbol]                       0x00000dec   Section        0  control.o(.text.Poistion_Error)
    __arm_cp.0_0                             0x00000ffc   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_1                             0x00001000   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_2                             0x00001004   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_3                             0x00001008   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_4                             0x0000100c   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_5                             0x00001010   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_6                             0x00001014   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_7                             0x00001018   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_8                             0x0000101c   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_9                             0x00001020   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_10                            0x00001024   Number         4  control.o(.text.Poistion_Error)
    __arm_cp.0_11                            0x00001028   Number         4  control.o(.text.Poistion_Error)
    [Anonymous Symbol]                       0x0000102c   Section        0  control.o(.text.Poistion_PID)
    __arm_cp.1_0                             0x0000108c   Number         4  control.o(.text.Poistion_PID)
    __arm_cp.1_1                             0x00001090   Number         4  control.o(.text.Poistion_PID)
    __arm_cp.1_2                             0x00001094   Number         4  control.o(.text.Poistion_PID)
    __arm_cp.1_3                             0x00001098   Number         4  control.o(.text.Poistion_PID)
    __arm_cp.1_4                             0x0000109c   Number         4  control.o(.text.Poistion_PID)
    __arm_cp.1_5                             0x000010a0   Number         4  control.o(.text.Poistion_PID)
    __arm_cp.1_6                             0x000010a4   Number         4  control.o(.text.Poistion_PID)
    [Anonymous Symbol]                       0x000010a8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_0                             0x000010d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_1                             0x000010dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_2                             0x000010e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_5                             0x000010e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    [Anonymous Symbol]                       0x000010e8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_0                             0x0000112c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_1                             0x00001130   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_2                             0x00001134   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_3                             0x00001138   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_4                             0x0000113c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_5                             0x00001140   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    __arm_cp.6_6                             0x00001144   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    [Anonymous Symbol]                       0x00001148   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x000011ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x000011b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000011b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x000011b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x000011bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x000011c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x000011c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x000011c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000011cc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001200   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00001204   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001208   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.8_0                             0x00001220   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001224   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_0                             0x00001254   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_1                             0x00001258   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_2                             0x0000125c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_3                             0x00001260   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_4                             0x00001264   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.4_5                             0x00001268   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x0000126c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_0                             0x000012c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_1                             0x000012c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_2                             0x000012c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_3                             0x000012cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_4                             0x000012d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_5                             0x000012d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x000012d8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x000012fc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001330   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001334   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00001338   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x0000133c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00001340   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00001344   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00001348   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x0000134c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001350   Section        0  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_0                             0x00001370   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_1                             0x00001374   Number         4  empty.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00001378   Section        0  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_0                             0x000013c4   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_2                             0x000013c8   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_3                             0x000013cc   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.9_4                             0x000013d0   Number         4  board.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x000013d4   Section        0  board.o(.text._sys_exit)
    [Anonymous Symbol]                       0x000013d8   Section        0  board.o(.text.board_init)
    __arm_cp.0_0                             0x000013f4   Number         4  board.o(.text.board_init)
    __arm_cp.0_1                             0x000013f8   Number         4  board.o(.text.board_init)
    [Anonymous Symbol]                       0x0000141c   Section        0  drv_oled.o(.text.bsp_analog_i2c_init)
    __arm_cp.0_1                             0x000014fc   Number         4  drv_oled.o(.text.bsp_analog_i2c_init)
    [Anonymous Symbol]                       0x00001500   Section        0  drv_oled.o(.text.bsp_analog_i2c_send_byte)
    __arm_cp.8_0                             0x000016d8   Number         4  drv_oled.o(.text.bsp_analog_i2c_send_byte)
    __arm_cp.8_1                             0x000016dc   Number         4  drv_oled.o(.text.bsp_analog_i2c_send_byte)
    __arm_cp.8_2                             0x000016e0   Number         4  drv_oled.o(.text.bsp_analog_i2c_send_byte)
    __arm_cp.8_3                             0x000016e4   Number         4  drv_oled.o(.text.bsp_analog_i2c_send_byte)
    [Anonymous Symbol]                       0x000016e8   Section        0  drv_oled.o(.text.display_6_8_string)
    [Anonymous Symbol]                       0x000016f0   Section        0  board.o(.text.fputc)
    __arm_cp.8_0                             0x00001710   Number         4  board.o(.text.fputc)
    [Anonymous Symbol]                       0x00001714   Section        0  huiduo.o(.text.huidu_read)
    __arm_cp.0_0                             0x00001a04   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_1                             0x00001a08   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_2                             0x00001a0c   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_3                             0x00001a10   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_4                             0x00001a14   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_5                             0x00001a18   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_6                             0x00001a1c   Number         4  huiduo.o(.text.huidu_read)
    __arm_cp.0_7                             0x00001a20   Number         4  huiduo.o(.text.huidu_read)
    [Anonymous Symbol]                       0x00001a24   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00001a8c   Number         4  empty.o(.text.main)
    __arm_cp.0_1                             0x00001a90   Number         4  empty.o(.text.main)
    __arm_cp.0_3                             0x00001a9c   Number         4  empty.o(.text.main)
    __arm_cp.0_4                             0x00001aa0   Number         4  empty.o(.text.main)
    __arm_cp.0_5                             0x00001aa4   Number         4  empty.o(.text.main)
    __arm_cp.0_6                             0x00001aa8   Number         4  empty.o(.text.main)
    __arm_cp.0_7                             0x00001aac   Number         4  empty.o(.text.main)
    __arm_cp.0_8                             0x00001ab0   Number         4  empty.o(.text.main)
    __arm_cp.0_9                             0x00001ab4   Number         4  empty.o(.text.main)
    __arm_cp.0_10                            0x00001ab8   Number         4  empty.o(.text.main)
    __arm_cp.0_11                            0x00001abc   Number         4  empty.o(.text.main)
    __arm_cp.0_12                            0x00001ac0   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00001ac4   Section        0  drv_oled.o(.text.oled_init)
    [Anonymous Symbol]                       0x00001afc   Section        0  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_0                             0x00001c04   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_1                             0x00001c08   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_2                             0x00001c0c   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_3                             0x00001c10   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_4                             0x00001c14   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_5                             0x00001c18   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_6                             0x00001c1c   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_7                             0x00001c20   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_8                             0x00001c24   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_9                             0x00001c28   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_10                            0x00001c2c   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_11                            0x00001c30   Number         4  ssd1306.o(.text.ssd1306_begin)
    __arm_cp.4_12                            0x00001c34   Number         4  ssd1306.o(.text.ssd1306_begin)
    i._is_digit                              0x00001c38   Section        0  __printf_wp.o(i._is_digit)
    x$fpl$fadd                               0x00001c48   Section      140  faddsub.o(x$fpl$fadd)
    _fadd1                                   0x00001c55   Thumb Code     0  faddsub.o(x$fpl$fadd)
    x$fpl$fmul                               0x00001cd4   Section      176  fmul.o(x$fpl$fmul)
    x$fpl$fsub                               0x00001d84   Section      208  faddsub.o(x$fpl$fsub)
    _fsub1                                   0x00001d91   Thumb Code     0  faddsub.o(x$fpl$fsub)
    maptable                                 0x00001e54   Data          17  __printf_flags_wp.o(.constdata)
    .constdata                               0x00001e54   Section       17  __printf_flags_wp.o(.constdata)
    x$fpl$usenofp                            0x00001e54   Section        0  usenofp.o(x$fpl$usenofp)
    gADC1ClockConfig                         0x00002490   Data           8  ti_msp_dl_config.o(.rodata.gADC1ClockConfig)
    [Anonymous Symbol]                       0x00002490   Section        0  ti_msp_dl_config.o(.rodata.gADC1ClockConfig)
    gAOClockConfig                           0x00002498   Data           8  ti_msp_dl_config.o(.rodata.gAOClockConfig)
    [Anonymous Symbol]                       0x00002498   Section        0  ti_msp_dl_config.o(.rodata.gAOClockConfig)
    gTIMER_0ClockConfig                      0x000024a0   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x000024a0   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x000024a4   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x000024a4   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x000024b8   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000024b8   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000024ba   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000024ba   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000024c4   Section        0  empty.o(.rodata.str1.1)
    .bss                                     0x20200000   Section       96  libspace.o(.bss)
    HEIGHT                                   0x20200080   Data           1  ssd1306.o(.bss.HEIGHT)
    [Anonymous Symbol]                       0x20200080   Section        0  ssd1306.o(.bss.HEIGHT)
    WIDTH                                    0x202000a0   Data           1  ssd1306.o(.bss.WIDTH)
    [Anonymous Symbol]                       0x202000a0   Section        0  ssd1306.o(.bss.WIDTH)
    _height                                  0x202000fa   Data           2  ssd1306.o(.bss._height)
    [Anonymous Symbol]                       0x202000fa   Section        0  ssd1306.o(.bss._height)
    _vccstate                                0x202000fc   Data           1  ssd1306.o(.bss._vccstate)
    [Anonymous Symbol]                       0x202000fc   Section        0  ssd1306.o(.bss._vccstate)
    _width                                   0x202000fe   Data           2  ssd1306.o(.bss._width)
    [Anonymous Symbol]                       0x202000fe   Section        0  ssd1306.o(.bss._width)
    cursor_x                                 0x20200104   Data           2  ssd1306.o(.bss.cursor_x)
    [Anonymous Symbol]                       0x20200104   Section        0  ssd1306.o(.bss.cursor_x)
    cursor_y                                 0x20200106   Data           2  ssd1306.o(.bss.cursor_y)
    [Anonymous Symbol]                       0x20200106   Section        0  ssd1306.o(.bss.cursor_y)
    rotation                                 0x2020018e   Data           1  ssd1306.o(.bss.rotation)
    [Anonymous Symbol]                       0x2020018e   Section        0  ssd1306.o(.bss.rotation)
    textbgcolor                              0x202001a4   Data           2  ssd1306.o(.bss.textbgcolor)
    [Anonymous Symbol]                       0x202001a4   Section        0  ssd1306.o(.bss.textbgcolor)
    textcolor                                0x202001a6   Data           2  ssd1306.o(.bss.textcolor)
    [Anonymous Symbol]                       0x202001a6   Section        0  ssd1306.o(.bss.textcolor)
    textsize                                 0x202001a8   Data           1  ssd1306.o(.bss.textsize)
    [Anonymous Symbol]                       0x202001a8   Section        0  ssd1306.o(.bss.textsize)
    Heap_Mem                                 0x202001b0   Data           0  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x202001b0   Data         256  startup_mspm0g350x_uvision.o(STACK)
    HEAP                                     0x202001b0   Section        0  startup_mspm0g350x_uvision.o(HEAP)
    STACK                                    0x202001b0   Section      256  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x202002b0   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x00000121   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000129   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000145   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x00000147   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x00000151   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x00000155   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000157   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000159   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000015b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000015d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000015d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000015d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000163   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000163   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000167   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000167   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000016f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000171   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000171   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000175   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000017d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x00000181   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x00000183   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x00000185   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x00000187   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x00000189   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x0000018b   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x0000018b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x0000018d   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __2sprintf                               0x000001a9   Thumb Code    36  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x000001d1   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x000001fd   Thumb Code    34  _printf_pad.o(.text)
    _printf_int_dec                          0x00000221   Thumb Code    90  _printf_dec.o(.text)
    __printf                                 0x0000028d   Thumb Code   306  __printf_flags_wp.o(.text)
    puts                                     0x000003c5   Thumb Code    38  puts.o(.text)
    __use_two_region_memory                  0x000003f1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x000003f3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000003f5   Thumb Code     2  heapauxi.o(.text)
    __aeabi_f2iz                             0x000003f7   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x000003f7   Thumb Code    76  ffixi.o(.text)
    __aeabi_i2f_normalise                    0x00000443   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x0000048b   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x0000048b   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x0000049b   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x0000049b   Thumb Code     0  fflti.o(.text)
    _printf_int_common                       0x000004a1   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_char_common                      0x0000055b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x00000581   Thumb Code    10  _sputc.o(.text)
    __rt_udiv10                              0x0000058b   Thumb Code    40  rtudiv10.o(.text)
    __user_libspace                          0x000005b5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x000005b5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x000005b5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x000005bd   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x000005fb   Thumb Code    16  exit.o(.text)
    ADC0_IRQHandler                          0x0000060d   Thumb Code    20  ccd.o(.text.ADC0_IRQHandler)
    DL_ADC12_setClockConfig                  0x00000629   Thumb Code    56  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00000669   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_Timer_initTimerMode                   0x00000675   Thumb Code   224  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setClockConfig                  0x00000765   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000781   Thumb Code    64  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000007c9   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    Draw_Logo                                0x000007dd   Thumb Code   312  drv_oled.o(.text.Draw_Logo)
    LCD_P6x8Str                              0x00000921   Thumb Code   136  drv_oled.o(.text.LCD_P6x8Str)
    OLED_Fill                                0x000009ad   Thumb Code   278  drv_oled.o(.text.OLED_Fill)
    OLED_WrCmd                               0x00000ac5   Thumb Code   400  drv_oled.o(.text.OLED_WrCmd)
    OLED_WrDat                               0x00000c55   Thumb Code   400  drv_oled.o(.text.OLED_WrDat)
    Poistion_Error                           0x00000ded   Thumb Code   528  control.o(.text.Poistion_Error)
    Poistion_PID                             0x0000102d   Thumb Code    96  control.o(.text.Poistion_PID)
    SYSCFG_DL_ADC1_init                      0x000010a9   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    SYSCFG_DL_AO_init                        0x000010e9   Thumb Code    68  ti_msp_dl_config.o(.text.SYSCFG_DL_AO_init)
    SYSCFG_DL_GPIO_init                      0x00001149   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_SYSCTL_init                    0x000011cd   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001209   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00001225   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x0000126d   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x000012d9   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x000012fd   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    TIMG0_IRQHandler                         0x00001351   Thumb Code    32  empty.o(.text.TIMG0_IRQHandler)
    UART0_IRQHandler                         0x00001379   Thumb Code    76  board.o(.text.UART0_IRQHandler)
    _sys_exit                                0x000013d5   Thumb Code     2  board.o(.text._sys_exit)
    board_init                               0x000013d9   Thumb Code    28  board.o(.text.board_init)
    bsp_analog_i2c_init                      0x0000141d   Thumb Code   224  drv_oled.o(.text.bsp_analog_i2c_init)
    bsp_analog_i2c_send_byte                 0x00001501   Thumb Code   472  drv_oled.o(.text.bsp_analog_i2c_send_byte)
    display_6_8_string                       0x000016e9   Thumb Code     8  drv_oled.o(.text.display_6_8_string)
    fputc                                    0x000016f1   Thumb Code    32  board.o(.text.fputc)
    huidu_read                               0x00001715   Thumb Code   752  huiduo.o(.text.huidu_read)
    main                                     0x00001a25   Thumb Code   104  empty.o(.text.main)
    oled_init                                0x00001ac5   Thumb Code    56  drv_oled.o(.text.oled_init)
    ssd1306_begin                            0x00001afd   Thumb Code   264  ssd1306.o(.text.ssd1306_begin)
    _is_digit                                0x00001c39   Thumb Code    14  __printf_wp.o(i._is_digit)
    __aeabi_fadd                             0x00001c49   Thumb Code     0  faddsub.o(x$fpl$fadd)
    _fadd                                    0x00001c49   Thumb Code   134  faddsub.o(x$fpl$fadd)
    __aeabi_fmul                             0x00001cd5   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x00001cd5   Thumb Code   172  fmul.o(x$fpl$fmul)
    __aeabi_fsub                             0x00001d85   Thumb Code     0  faddsub.o(x$fpl$fsub)
    _fsub                                    0x00001d85   Thumb Code   204  faddsub.o(x$fpl$fsub)
    __I$use$fp                               0x00001e54   Number         0  usenofp.o(x$fpl$usenofp)
    F6x8                                     0x00001e65   Data         552  drv_oled.o(.rodata.F6x8)
    NC_Logo                                  0x0000208d   Data        1024  drv_oled.o(.rodata.NC_Logo)
    Region$$Table$$Base                      0x000024e0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000024f0   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200000   Data          96  libspace.o(.bss)
    HD2adc_value                             0x20200060   Data          16  huiduo.o(.bss.HD2adc_value)
    __temporary_stack_top$libspace           0x20200060   Data           0  libspace.o(.bss)
    HDadc_value                              0x20200070   Data          16  huiduo.o(.bss.HDadc_value)
    Kd                                       0x20200084   Data           4  control.o(.bss.Kd)
    Kp                                       0x20200088   Data           4  control.o(.bss.Kp)
    Pos_error_last                           0x2020008c   Data           2  control.o(.bss.Pos_error_last)
    Pos_error_now                            0x2020008e   Data           2  control.o(.bss.Pos_error_now)
    Position                                 0x20200090   Data           4  control.o(.bss.Position)
    Position2                                0x20200094   Data           2  control.o(.bss.Position2)
    Position2_Last                           0x20200096   Data           2  control.o(.bss.Position2_Last)
    Read_Speed_L                             0x20200098   Data           4  motor.o(.bss.Read_Speed_L)
    Read_Speed_R                             0x2020009c   Data           4  motor.o(.bss.Read_Speed_R)
    __stdout                                 0x202000a4   Data          84  board.o(.bss.__stdout)
    _cp437                                   0x202000f8   Data           1  ssd1306.o(.bss._cp437)
    cnt                                      0x20200100   Data           4  empty.o(.bss.cnt)
    gCheckADC                                0x20200108   Data           1  ccd.o(.bss.gCheckADC)
    i                                        0x20200109   Data           1  huiduo.o(.bss.i)
    lsqgCheckADC                             0x2020010a   Data           1  huiduo.o(.bss.lsqgCheckADC)
    recv0_buff                               0x2020010b   Data         128  board.o(.bss.recv0_buff)
    recv0_flag                               0x2020018b   Data           1  board.o(.bss.recv0_flag)
    recv0_length                             0x2020018c   Data           2  board.o(.bss.recv0_length)
    str                                      0x2020018f   Data          20  empty.o(.bss.str)
    wrap                                     0x202001a9   Data           1  ssd1306.o(.bss.wrap)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000024f0, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000024f0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           21    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO         1446  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1763    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x00000002   Code   RO         1764    !!handler_null      c_p.l(__scatter.o)
    0x00000122   0x00000122   0x00000006   PAD
    0x00000128   0x00000128   0x0000001c   Code   RO         1767    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000144   0x00000144   0x00000002   Code   RO         1429    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000146   0x00000146   0x0000000a   Code   RO         1428    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x00000150   0x00000150   0x00000004   Code   RO         1530    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x00000154   0x00000154   0x00000002   Code   RO         1623    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1640    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1642    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1644    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1647    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1649    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1651    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1654    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1656    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1658    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1660    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1662    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1664    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1666    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1668    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1670    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1672    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1674    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1678    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1680    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1682    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1684    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000156   0x00000156   0x00000002   Code   RO         1685    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000158   0x00000158   0x00000002   Code   RO         1715    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000015a   0x0000015a   0x00000000   Code   RO         1746    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000015a   0x0000015a   0x00000000   Code   RO         1748    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000015a   0x0000015a   0x00000000   Code   RO         1751    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000015a   0x0000015a   0x00000000   Code   RO         1754    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000015a   0x0000015a   0x00000000   Code   RO         1756    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000015a   0x0000015a   0x00000000   Code   RO         1759    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000015a   0x0000015a   0x00000002   Code   RO         1760    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000015c   0x0000015c   0x00000000   Code   RO         1509    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000015c   0x0000015c   0x00000000   Code   RO         1573    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000015c   0x0000015c   0x00000006   Code   RO         1585    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000162   0x00000162   0x00000000   Code   RO         1575    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000162   0x00000162   0x00000004   Code   RO         1576    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000166   0x00000166   0x00000000   Code   RO         1578    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000166   0x00000166   0x00000008   Code   RO         1579    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000016e   0x0000016e   0x00000002   Code   RO         1631    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000170   0x00000170   0x00000000   Code   RO         1689    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000170   0x00000170   0x00000004   Code   RO         1690    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000174   0x00000174   0x00000006   Code   RO         1691    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000017a   0x0000017a   0x00000002   PAD
    0x0000017c   0x0000017c   0x0000002c   Code   RO           22    .text               startup_mspm0g350x_uvision.o
    0x000001a8   0x000001a8   0x00000028   Code   RO         1378    .text               c_p.l(noretval__2sprintf.o)
    0x000001d0   0x000001d0   0x0000004e   Code   RO         1382    .text               c_p.l(_printf_pad.o)
    0x0000021e   0x0000021e   0x00000002   PAD
    0x00000220   0x00000220   0x0000006c   Code   RO         1384    .text               c_p.l(_printf_dec.o)
    0x0000028c   0x0000028c   0x00000138   Code   RO         1419    .text               c_p.l(__printf_flags_wp.o)
    0x000003c4   0x000003c4   0x0000002c   Code   RO         1430    .text               c_p.l(puts.o)
    0x000003f0   0x000003f0   0x00000006   Code   RO         1444    .text               c_p.l(heapauxi.o)
    0x000003f6   0x000003f6   0x0000004c   Code   RO         1494    .text               fz_ps.l(ffixi.o)
    0x00000442   0x00000442   0x0000005e   Code   RO         1496    .text               fz_ps.l(fflti.o)
    0x000004a0   0x000004a0   0x000000b0   Code   RO         1522    .text               c_p.l(_printf_intcommon.o)
    0x00000550   0x00000550   0x00000030   Code   RO         1524    .text               c_p.l(_printf_char_common.o)
    0x00000580   0x00000580   0x0000000a   Code   RO         1526    .text               c_p.l(_sputc.o)
    0x0000058a   0x0000058a   0x00000028   Code   RO         1531    .text               c_p.l(rtudiv10.o)
    0x000005b2   0x000005b2   0x00000002   PAD
    0x000005b4   0x000005b4   0x00000008   Code   RO         1606    .text               c_p.l(libspace.o)
    0x000005bc   0x000005bc   0x0000003e   Code   RO         1609    .text               c_p.l(sys_stackheap_outer.o)
    0x000005fa   0x000005fa   0x00000010   Code   RO         1612    .text               c_p.l(exit.o)
    0x0000060a   0x0000060a   0x00000002   PAD
    0x0000060c   0x0000060c   0x0000001c   Code   RO          514    .text.ADC0_IRQHandler  ccd.o
    0x00000628   0x00000628   0x00000040   Code   RO          641    .text.DL_ADC12_setClockConfig  dl_adc12.o
    0x00000668   0x00000668   0x0000000a   Code   RO          690    .text.DL_Common_delayCycles  dl_common.o
    0x00000672   0x00000672   0x00000002   PAD
    0x00000674   0x00000674   0x000000f0   Code   RO         1208    .text.DL_Timer_initTimerMode  dl_timer.o
    0x00000764   0x00000764   0x0000001c   Code   RO         1204    .text.DL_Timer_setClockConfig  dl_timer.o
    0x00000780   0x00000780   0x00000048   Code   RO         1313    .text.DL_UART_init  dl_uart.o
    0x000007c8   0x000007c8   0x00000012   Code   RO         1315    .text.DL_UART_setClockConfig  dl_uart.o
    0x000007da   0x000007da   0x00000002   PAD
    0x000007dc   0x000007dc   0x00000144   Code   RO          361    .text.Draw_Logo     drv_oled.o
    0x00000920   0x00000920   0x0000008c   Code   RO          353    .text.LCD_P6x8Str   drv_oled.o
    0x000009ac   0x000009ac   0x00000116   Code   RO          349    .text.OLED_Fill     drv_oled.o
    0x00000ac2   0x00000ac2   0x00000002   PAD
    0x00000ac4   0x00000ac4   0x00000190   Code   RO          345    .text.OLED_WrCmd    drv_oled.o
    0x00000c54   0x00000c54   0x00000198   Code   RO          343    .text.OLED_WrDat    drv_oled.o
    0x00000dec   0x00000dec   0x00000240   Code   RO          576    .text.Poistion_Error  control.o
    0x0000102c   0x0000102c   0x0000007c   Code   RO          578    .text.Poistion_PID  control.o
    0x000010a8   0x000010a8   0x00000040   Code   RO           43    .text.SYSCFG_DL_ADC1_init  ti_msp_dl_config.o
    0x000010e8   0x000010e8   0x00000060   Code   RO           41    .text.SYSCFG_DL_AO_init  ti_msp_dl_config.o
    0x00001148   0x00001148   0x00000084   Code   RO           33    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000011cc   0x000011cc   0x0000003c   Code   RO           35    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001208   0x00001208   0x0000001c   Code   RO           45    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001224   0x00001224   0x00000048   Code   RO           37    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x0000126c   0x0000126c   0x0000006c   Code   RO           39    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x000012d8   0x000012d8   0x00000024   Code   RO           29    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x000012fc   0x000012fc   0x00000054   Code   RO           31    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001350   0x00001350   0x00000028   Code   RO            6    .text.TIMG0_IRQHandler  empty.o
    0x00001378   0x00001378   0x0000005c   Code   RO           79    .text.UART0_IRQHandler  board.o
    0x000013d4   0x000013d4   0x00000002   Code   RO           75    .text._sys_exit     board.o
    0x000013d6   0x000013d6   0x00000002   PAD
    0x000013d8   0x000013d8   0x00000044   Code   RO           61    .text.board_init    board.o
    0x0000141c   0x0000141c   0x000000e4   Code   RO          323    .text.bsp_analog_i2c_init  drv_oled.o
    0x00001500   0x00001500   0x000001e8   Code   RO          339    .text.bsp_analog_i2c_send_byte  drv_oled.o
    0x000016e8   0x000016e8   0x00000008   Code   RO          367    .text.display_6_8_string  drv_oled.o
    0x000016f0   0x000016f0   0x00000024   Code   RO           77    .text.fputc         board.o
    0x00001714   0x00001714   0x00000310   Code   RO          543    .text.huidu_read    huiduo.o
    0x00001a24   0x00001a24   0x000000a0   Code   RO            2    .text.main          empty.o
    0x00001ac4   0x00001ac4   0x00000038   Code   RO          363    .text.oled_init     drv_oled.o
    0x00001afc   0x00001afc   0x0000013c   Code   RO          393    .text.ssd1306_begin  ssd1306.o
    0x00001c38   0x00001c38   0x0000000e   Code   RO         1417    i._is_digit         c_p.l(__printf_wp.o)
    0x00001c46   0x00001c46   0x00000002   PAD
    0x00001c48   0x00001c48   0x0000008c   Code   RO         1461    x$fpl$fadd          fz_ps.l(faddsub.o)
    0x00001cd4   0x00001cd4   0x000000b0   Code   RO         1498    x$fpl$fmul          fz_ps.l(fmul.o)
    0x00001d84   0x00001d84   0x000000d0   Code   RO         1463    x$fpl$fsub          fz_ps.l(faddsub.o)
    0x00001e54   0x00001e54   0x00000000   Code   RO         1543    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x00001e54   0x00001e54   0x00000011   Data   RO         1420    .constdata          c_p.l(__printf_flags_wp.o)
    0x00001e65   0x00001e65   0x00000228   Data   RO          375    .rodata.F6x8        drv_oled.o
    0x0000208d   0x0000208d   0x00000400   Data   RO          376    .rodata.NC_Logo     drv_oled.o
    0x0000248d   0x0000248d   0x00000003   PAD
    0x00002490   0x00002490   0x00000008   Data   RO           52    .rodata.gADC1ClockConfig  ti_msp_dl_config.o
    0x00002498   0x00002498   0x00000008   Data   RO           51    .rodata.gAOClockConfig  ti_msp_dl_config.o
    0x000024a0   0x000024a0   0x00000003   Data   RO           47    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x000024a3   0x000024a3   0x00000001   PAD
    0x000024a4   0x000024a4   0x00000014   Data   RO           48    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x000024b8   0x000024b8   0x00000002   Data   RO           49    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000024ba   0x000024ba   0x0000000a   Data   RO           50    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000024c4   0x000024c4   0x00000018   Data   RO           10    .rodata.str1.1      empty.o
    0x000024dc   0x000024dc   0x00000004   PAD
    0x000024e0   0x000024e0   0x00000010   Data   RO         1762    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000024f0, Size: 0x000002b0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000        -       0x00000060   Zero   RW         1607    .bss                c_p.l(libspace.o)
    0x20200060        -       0x00000010   Zero   RW          552    .bss.HD2adc_value   huiduo.o
    0x20200070        -       0x00000010   Zero   RW          551    .bss.HDadc_value    huiduo.o
    0x20200080        -       0x00000001   Zero   RW          489    .bss.HEIGHT         ssd1306.o
    0x20200081   0x000024f0   0x00000003   PAD
    0x20200084        -       0x00000004   Zero   RW          586    .bss.Kd             control.o
    0x20200088        -       0x00000004   Zero   RW          584    .bss.Kp             control.o
    0x2020008c        -       0x00000002   Zero   RW          585    .bss.Pos_error_last  control.o
    0x2020008e        -       0x00000002   Zero   RW          583    .bss.Pos_error_now  control.o
    0x20200090        -       0x00000004   Zero   RW          582    .bss.Position       control.o
    0x20200094        -       0x00000002   Zero   RW          587    .bss.Position2      control.o
    0x20200096        -       0x00000002   Zero   RW          588    .bss.Position2_Last  control.o
    0x20200098        -       0x00000004   Zero   RW          619    .bss.Read_Speed_L   motor.o
    0x2020009c        -       0x00000004   Zero   RW          626    .bss.Read_Speed_R   motor.o
    0x202000a0        -       0x00000001   Zero   RW          488    .bss.WIDTH          ssd1306.o
    0x202000a1   0x000024f0   0x00000003   PAD
    0x202000a4        -       0x00000054   Zero   RW           84    .bss.__stdout       board.o
    0x202000f8        -       0x00000001   Zero   RW          497    .bss._cp437         ssd1306.o
    0x202000f9   0x000024f0   0x00000001   PAD
    0x202000fa        -       0x00000002   Zero   RW          486    .bss._height        ssd1306.o
    0x202000fc        -       0x00000001   Zero   RW          490    .bss._vccstate      ssd1306.o
    0x202000fd   0x000024f0   0x00000001   PAD
    0x202000fe        -       0x00000002   Zero   RW          485    .bss._width         ssd1306.o
    0x20200100        -       0x00000004   Zero   RW            8    .bss.cnt            empty.o
    0x20200104        -       0x00000002   Zero   RW          491    .bss.cursor_x       ssd1306.o
    0x20200106        -       0x00000002   Zero   RW          492    .bss.cursor_y       ssd1306.o
    0x20200108        -       0x00000001   Zero   RW          528    .bss.gCheckADC      ccd.o
    0x20200109        -       0x00000001   Zero   RW          560    .bss.i              huiduo.o
    0x2020010a        -       0x00000001   Zero   RW          561    .bss.lsqgCheckADC   huiduo.o
    0x2020010b        -       0x00000080   Zero   RW           81    .bss.recv0_buff     board.o
    0x2020018b        -       0x00000001   Zero   RW           83    .bss.recv0_flag     board.o
    0x2020018c        -       0x00000002   Zero   RW           82    .bss.recv0_length   board.o
    0x2020018e        -       0x00000001   Zero   RW          487    .bss.rotation       ssd1306.o
    0x2020018f        -       0x00000014   Zero   RW            9    .bss.str            empty.o
    0x202001a3   0x000024f0   0x00000001   PAD
    0x202001a4        -       0x00000002   Zero   RW          494    .bss.textbgcolor    ssd1306.o
    0x202001a6        -       0x00000002   Zero   RW          495    .bss.textcolor      ssd1306.o
    0x202001a8        -       0x00000001   Zero   RW          493    .bss.textsize       ssd1306.o
    0x202001a9        -       0x00000001   Zero   RW          496    .bss.wrap           ssd1306.o
    0x202001aa   0x000024f0   0x00000006   PAD
    0x202001b0        -       0x00000000   Zero   RW           20    HEAP                startup_mspm0g350x_uvision.o
    0x202001b0        -       0x00000100   Zero   RW           19    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       198         60          0          0        215       9504   board.o
        28          8          0          0          1       9840   ccd.o
       700         76          0          0         20       1815   control.o
        64          8          0          0          0       4485   dl_adc12.o
        10          0          0          0          0        568   dl_common.o
       268        160          0          0          0      36777   dl_timer.o
        90          8          0          0          0      14658   dl_uart.o
      2330         44       1576          0          0      26152   drv_oled.o
       200         64         24          0         24       6041   empty.o
       784         32          0          0         34      11710   huiduo.o
         0          0          0          0          8       1107   motor.o
       316         52          0          0         19      33539   ssd1306.o
        44         18        192          0        256        668   startup_mspm0g350x_uvision.o
       680        168         51          0          0      31112   ti_msp_dl_config.o

    ----------------------------------------------------------------------
      5720        <USER>       <GROUP>          0        592     187976   Object Totals
         0          0         16          0          0          0   (incl. Generated)
         8          0          8          0         15          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       312          6         17          0          0         76   __printf_flags_wp.o
        14          0          0          0          0         60   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         88   _printf_char_common.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
       176          0          0          0          0         84   _printf_intcommon.o
        78          0          0          0          0        100   _printf_pad.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         60   _sputc.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        40          4          0          0          0         84   noretval__2sprintf.o
        44          6          0          0          0         68   puts.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        62          0          0          0          0         80   sys_stackheap_outer.o
       348          8          0          0          0        160   faddsub.o
        76          0          0          0          0         68   ffixi.o
        94          0          0          0          0         92   fflti.o
       176          4          0          0          0         80   fmul.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      1852         <USER>         <GROUP>          0         96       1576   Library Totals
        20          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1138         54         17          0         96       1176   c_p.l
       694         12          0          0          0        400   fz_ps.l

    ----------------------------------------------------------------------
      1852         <USER>         <GROUP>          0         96       1576   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7572        768       1884          0        688     188652   Grand Totals
      7572        768       1884          0        688     188652   ELF Image Totals
      7572        768       1884          0          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9456 (   9.23kB)
    Total RW  Size (RW Data + ZI Data)               688 (   0.67kB)
    Total ROM Size (Code + RO Data + RW Data)       9456 (   9.23kB)

==============================================================================

