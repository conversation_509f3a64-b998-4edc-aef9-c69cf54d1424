#include "ALLHeader.h"

uint16_t Time_Count;
uint8_t Turn_Left_Flag,Turn_Right_Flag,Turn180_Flag;

int16_t Position2,AD_L_R_Diff,Pos_error_now,Pos_error_last,Position2_Last;
float Kp,Kd,Kd_tly,Position,Target_S,Kp_S,Kd_S;
uint8_t Stop_Flag,State,Turn_dis_Flag;
uint8_t sensor[5]={0};
uint16_t Distance[4]={0},Find_Distance;
uint16_t Distance_01;
uint8_t Stop_Count;
uint8_t Pass_Flag;
uint8_t Zero_Flag,Stop_All_Flag;

int Poistion_Error(void)//获取偏差值 - 优化的灰度传感器算法
{
    // 灰度传感器阵列位置权重计算
    // 传感器编号: HD2[7] HD[1] HD[2] HD[3] HD[4] HD[5] HD[6] HD[7]
    // 位置权重:   -7    -3    -2    -1     1     2     3     7
    // 左偏差值为正，右偏差值为负

    float weighted_sum = 0;
    int sensor_count = 0;

    // 计算加权位置和
    if(HD2adc_value[7] == 1) { weighted_sum += (-7); sensor_count++; }  // 最左传感器
    if(HDadc_value[1] == 1)  { weighted_sum += (-3); sensor_count++; }  // 左侧传感器
    if(HDadc_value[2] == 1)  { weighted_sum += (-2); sensor_count++; }  // 左中传感器
    if(HDadc_value[3] == 1)  { weighted_sum += (-1); sensor_count++; }  // 中左传感器
    if(HDadc_value[4] == 1)  { weighted_sum += (1);  sensor_count++; }  // 中右传感器
    if(HDadc_value[5] == 1)  { weighted_sum += (2);  sensor_count++; }  // 右中传感器
    if(HDadc_value[6] == 1)  { weighted_sum += (3);  sensor_count++; }  // 右侧传感器
    if(HDadc_value[7] == 1)  { weighted_sum += (7);  sensor_count++; }  // 最右传感器

    // 计算位置误差
    if(sensor_count > 0) {
        Position = weighted_sum / sensor_count;  // 加权平均位置
    } else {
        Position = 0;  // 没有检测到线条，保持当前方向
    }

    // 限制位置误差范围
    if(Position > 7) Position = 7;
    if(Position < -7) Position = -7;

    return Position;
}

//int ADC_Averge(void)
//{
// //��ֵ�˲�     
//  float temp=0,erroor[5],error_sum=0;
//  int i;
//  for(i=0;i<5;i++){
//  erroor[i] =Poistion_Error();//�õ�ƫ��ֵ
//  }//�õ�ƫ��ֵ  

////�ۼ�ƫ����ƽ��ֵ
//  for(i=0;i<5;i++)       error_sum=error_sum+erroor[i];
//  error_sum=error_sum/i;
//  return error_sum;
//}

void Poistion_PID(void)
{
//	if(State==0||State==2||State==3)
//	{
		Pos_error_now=Poistion_Error();
		Position2=Pos_error_now*Kp+(Pos_error_now-Pos_error_last)*Kd;	//+Real_Angle_Speed*kd_tly
//	}
	
	Position2=Position2>500? 500:Position2;				
	Position2=Position2<-500? -500:Position2; 				
	
	Pos_error_last=Pos_error_now;
	
	Position2_Last=Position2;	

}
