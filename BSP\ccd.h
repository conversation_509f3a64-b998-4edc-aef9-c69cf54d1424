#ifndef __CCD_H
#define __CCD_H	

#include "ALLHeader.h"

#define TSL_SI(state) ((state) ? DL_GPIO_setPins(CCD_PORT, CCD_SI_PIN) : DL_GPIO_clearPins(CCD_PORT, CCD_SI_PIN))   //SI  
#define TSL_CLK(state) ((state) ? DL_GPIO_setPins(CCD_PORT, CCD_CLK_PIN) : DL_GPIO_clearPins(CCD_PORT, CCD_CLK_PIN))  //CLK

unsigned int adc_getValue(void);
void RD_TSL(void); 
void slove_data(void);
void sendToPc(void);
void deal_data_ccd(void);
void  Find_CCD_Zhongzhi(void);

#endif 


